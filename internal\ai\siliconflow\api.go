package siliconflow

import "fmt"

// GenerateImage generates an image using SiliconFlow
//
// This is the main public API function for image generation with SiliconFlow.
// It uses the singleton instance of SiliconFlowService to handle the request.
//
// Parameters:
//   - prompt: The text description of the image to generate
//   - config: Configuration options for the image generation (can be nil for defaults)
//
// Returns:
//   - Either the URL of the generated image or the path where it was saved
//   - Any error that occurred during the process
func GenerateImage(prompt string, config *ImageGenerationConfig) (string, error) {
	// Set default values
	if config == nil {
		config = &ImageGenerationConfig{
			Width:     1024,
			Height:    1024,
			ReturnURL: true,
		}
	}

	// If output path is specified, return binary data
	outputPath := ""
	if config.ReturnURL == false {
		outputPath = "output.png" // Default output path
	}

	// Generate image
	result, err := GetSiliconFlowService().GenerateImageWithSiliconFlow(prompt, config)
	if err != nil {
		return "", fmt.Errorf("failed to generate image: %w", err)
	}

	// If output path is specified and image data exists, save the image
	if outputPath != "" && result.Data != nil {
		return GetSiliconFlowService().SaveImage(result.Data, outputPath)
	}

	// Otherwise return URL
	return result.URL, nil
}
