package deepseek

// DeepSeekMessage represents a message for the DeepSeek API
type DeepSeekMessage struct {
	// Role defines the message sender (system, user, assistant)
	Role string `json:"role"`
	// Content contains the actual message text
	Content string `json:"content"`
}

// DeepSeekRequestBody represents the request body for the DeepSeek API
type DeepSeekRequestBody struct {
	// Model specifies which AI model to use
	Model string `json:"model"`
	// Messages contains the conversation history
	Messages []DeepSeekMessage `json:"messages"`
}

// DeepSeekChoice represents a choice in the DeepSeek API response
type DeepSeekChoice struct {
	// Index is the position of this choice in the array of choices
	Index int `json:"index"`
	// Message contains the response content
	Message DeepSeekMessage `json:"message"`
	// FinishReason indicates why the model stopped generating text
	FinishReason string `json:"finish_reason"`
}

// DeepSeekResponseBody represents the response body from the DeepSeek API
type DeepSeekResponseBody struct {
	// ID is the unique identifier for this response
	ID string `json:"id"`
	// Choices contains the array of generated responses
	Choices []DeepSeekChoice `json:"choices"`
}

// GenerateTextOptions represents configuration options for text generation requests
type GenerateTextOptions struct {
	// Model specifies which AI model to use (overrides the default)
	Model string
	// Temperature controls randomness (0-1, higher = more random)
	Temperature float64
	// MaxTokens limits the maximum number of tokens in the response
	MaxTokens int
	// TopP controls diversity via nucleus sampling
	TopP float64
	// LogPrefix is prepended to log messages for this request
	LogPrefix string
}
