package handlers

import (
	"net/http"

	"github.com/gin-ai-backend/internal/auth"
	"github.com/gin-ai-backend/internal/database"
	"github.com/gin-ai-backend/internal/errors"
	"github.com/gin-ai-backend/internal/models"
	"github.com/gin-ai-backend/internal/pagination"
	"github.com/gin-ai-backend/internal/validation"
	"github.com/gin-gonic/gin"
)

// AuthHandler handles authentication-related requests
type AuthHandler struct {
	JWTSecret string
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(jwtSecret string) *AuthHandler {
	return &AuthHandler{
		JWTSecret: jwtSecret,
	}
}

// Register handles user registration
// @Summary Register a new user
// @Description Register a new user with username, email, and password
// @Tags auth
// @Accept json
// @Produce json
// @Param user body models.UserRegistrationRequest true "User Registration"
// @Success 201 {object} models.User
// @Failure 400 {object} gin.H
// @Failure 409 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var request models.UserRegistrationRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		validation.HandleValidationErrors(c, err)
		return
	}

	// Additional validation
	if err := validation.ValidateUsername(request.Username); err != nil {
		errors.BadRequest(c, err.Error(), nil)
		return
	}

	if err := validation.ValidatePassword(request.Password); err != nil {
		errors.BadRequest(c, err.Error(), nil)
		return
	}

	if err := validation.ValidateEmail(request.Email); err != nil {
		errors.BadRequest(c, err.Error(), nil)
		return
	}

	// Check if username already exists
	var existingUser models.User
	if result := database.DB.Where("username = ?", request.Username).First(&existingUser); result.RowsAffected > 0 {
		errors.Conflict(c, "Username already exists", nil)
		return
	}

	// Check if email already exists
	if result := database.DB.Where("email = ?", request.Email).First(&existingUser); result.RowsAffected > 0 {
		errors.Conflict(c, "Email already exists", nil)
		return
	}

	// Hash password
	hashedPassword, err := auth.HashPassword(request.Password)
	if err != nil {
		errors.InternalServerError(c, "Failed to hash password", err)
		return
	}

	// Create user
	user := models.User{
		Username: request.Username,
		Email:    request.Email,
		Password: hashedPassword,
		Role:     "user", // Default role
	}

	if result := database.DB.Create(&user); result.Error != nil {
		errors.InternalServerError(c, "Failed to create user", result.Error)
		return
	}

	// Generate token
	token, err := auth.GenerateToken(user, h.JWTSecret)
	if err != nil {
		errors.InternalServerError(c, "Failed to generate token", err)
		return
	}

	// Don't return password in response
	user.Password = ""
	user.APIToken = token

	c.JSON(http.StatusCreated, gin.H{"message": "User registered successfully", "user": user})
}

// Login handles user login
// @Summary Login a user
// @Description Login with username and password
// @Tags auth
// @Accept json
// @Produce json
// @Param user body models.UserLoginRequest true "User Login"
// @Success 200 {object} models.User
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var request models.UserLoginRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		validation.HandleValidationErrors(c, err)
		return
	}

	// Find user
	var user models.User
	if result := database.DB.Where("username = ?", request.Username).First(&user); result.Error != nil {
		errors.Unauthorized(c, "Invalid username or password", nil)
		return
	}

	// Check password
	if !auth.CheckPasswordHash(request.Password, user.Password) {
		errors.Unauthorized(c, "Invalid username or password", nil)
		return
	}

	// Generate token
	token, err := auth.GenerateToken(user, h.JWTSecret)
	if err != nil {
		errors.InternalServerError(c, "Failed to generate token", err)
		return
	}

	// Don't return password in response
	user.Password = ""
	user.APIToken = token

	c.JSON(http.StatusOK, gin.H{"message": "Login successful", "user": user})
}

// GetProfile handles getting the user's profile
// @Summary Get user profile
// @Description Get the authenticated user's profile
// @Tags auth
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} models.User
// @Failure 401 {object} gin.H
// @Failure 404 {object} gin.H
// @Router /auth/profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		errors.Unauthorized(c, "User not authenticated", nil)
		return
	}

	var user models.User
	if result := database.DB.First(&user, userID); result.Error != nil {
		errors.NotFound(c, "User not found", result.Error)
		return
	}

	// Don't return password in response
	user.Password = ""

	c.JSON(http.StatusOK, gin.H{"user": user})
}

// ListUsers handles listing users with pagination
// @Summary List users
// @Description Get a paginated list of users
// @Tags admin
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 10, max: 100)"
// @Success 200 {object} gin.H
// @Failure 401 {object} gin.H
// @Failure 403 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /admin/users [get]
func (h *AuthHandler) ListUsers(c *gin.Context) {
	// Check if user is admin
	role, exists := c.Get("role")
	if !exists || role != "admin" {
		errors.Forbidden(c, "Admin access required", nil)
		return
	}

	// Get users with pagination
	var users []models.User
	query := database.DB.Model(&models.User{})

	// Apply pagination
	count, err := pagination.Paginate(c, query, &users)
	if err != nil {
		errors.InternalServerError(c, "Failed to retrieve users", err)
		return
	}

	// Don't return passwords in response
	for i := range users {
		users[i].Password = ""
	}

	c.JSON(http.StatusOK, gin.H{
		"users":      users,
		"pagination": pagination.GetPaginationInfo(c, count),
	})
}
