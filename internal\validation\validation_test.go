package validation

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/assert"
)

// TestStruct for validation testing
type TestStruct struct {
	Name     string `validate:"required,min=3,max=50"`
	Email    string `validate:"required,email"`
	Age      int    `validate:"required,min=18,max=100"`
	Username string `validate:"required,alphanum"`
	Website  string `validate:"url"`
}

func setupValidationTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	return gin.New()
}

func TestValidateStruct_Success(t *testing.T) {
	testData := TestStruct{
		Name:     "John Doe",
		Email:    "<EMAIL>",
		Age:      25,
		Username: "johndoe123",
		Website:  "https://example.com",
	}

	err := ValidateStruct(testData)
	assert.NoError(t, err)
}

func TestValidateStruct_RequiredFields(t *testing.T) {
	testData := TestStruct{
		// Missing required fields
		Website: "https://example.com",
	}

	err := ValidateStruct(testData)
	assert.Error(t, err)

	validationErrors, ok := err.(validator.ValidationErrors)
	assert.True(t, ok)
	assert.Len(t, validationErrors, 4) // Name, Email, Age, Username are required
}

func TestValidateStruct_MinMaxValidation(t *testing.T) {
	testData := TestStruct{
		Name:     "Jo", // Too short (min=3)
		Email:    "<EMAIL>",
		Age:      17, // Too young (min=18)
		Username: "johndoe123",
	}

	err := ValidateStruct(testData)
	assert.Error(t, err)

	validationErrors, ok := err.(validator.ValidationErrors)
	assert.True(t, ok)
	assert.Len(t, validationErrors, 2) // Name and Age validation failures
}

func TestValidateStruct_EmailValidation(t *testing.T) {
	testData := TestStruct{
		Name:     "John Doe",
		Email:    "invalid-email", // Invalid email format
		Age:      25,
		Username: "johndoe123",
	}

	err := ValidateStruct(testData)
	assert.Error(t, err)

	validationErrors, ok := err.(validator.ValidationErrors)
	assert.True(t, ok)
	assert.Len(t, validationErrors, 1) // Email validation failure
}

func TestHandleValidationErrors_WithValidationErrors(t *testing.T) {
	router := setupValidationTestRouter()

	router.POST("/test", func(c *gin.Context) {
		testData := TestStruct{
			Name:     "Jo", // Too short
			Email:    "invalid-email",
			Age:      17, // Too young
			Username: "john doe", // Contains space (not alphanum)
		}

		err := ValidateStruct(testData)
		HandleValidationErrors(c, err)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, float64(400), response["code"])
	assert.Equal(t, "Validation failed", response["message"])
	assert.Contains(t, response, "errors")

	errors := response["errors"].(map[string]interface{})
	assert.Contains(t, errors, "name")
	assert.Contains(t, errors, "email")
	assert.Contains(t, errors, "age")
	assert.Contains(t, errors, "username")
}

func TestHandleValidationErrors_NoError(t *testing.T) {
	router := setupValidationTestRouter()

	router.POST("/test", func(c *gin.Context) {
		HandleValidationErrors(c, nil)
		c.JSON(200, gin.H{"message": "success"})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestHandleValidationErrors_NonValidationError(t *testing.T) {
	router := setupValidationTestRouter()

	router.POST("/test", func(c *gin.Context) {
		err := assert.AnError // Non-validation error
		HandleValidationErrors(c, err)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Invalid request data", response["message"])
}

func TestGetErrorMessage(t *testing.T) {
	// Create mock field errors for testing
	tests := []struct {
		tag      string
		param    string
		expected string
	}{
		{"required", "", "This field is required"},
		{"email", "", "Invalid email format"},
		{"min", "3", "Must be at least 3 characters long"},
		{"max", "50", "Must be at most 50 characters long"},
		{"alphanum", "", "Must contain only alphanumeric characters"},
		{"url", "", "Invalid URL format"},
		{"unknown", "", "Failed validation for 'unknown'"},
	}

	for _, test := range tests {
		// Create a mock field error
		mockError := &mockFieldError{
			tag:   test.tag,
			param: test.param,
		}

		result := getErrorMessage(mockError)
		assert.Equal(t, test.expected, result)
	}
}

// Mock implementation of validator.FieldError for testing
type mockFieldError struct {
	tag   string
	param string
}

func (m *mockFieldError) Tag() string                   { return m.tag }
func (m *mockFieldError) ActualTag() string             { return m.tag }
func (m *mockFieldError) Namespace() string             { return "" }
func (m *mockFieldError) StructNamespace() string       { return "" }
func (m *mockFieldError) Field() string                 { return "TestField" }
func (m *mockFieldError) StructField() string           { return "TestField" }
func (m *mockFieldError) Value() interface{}            { return nil }
func (m *mockFieldError) Param() string                 { return m.param }
func (m *mockFieldError) Kind() interface{}             { return nil }
func (m *mockFieldError) Type() interface{}             { return nil }
func (m *mockFieldError) Error() string                 { return "" }
func (m *mockFieldError) Translate(interface{}) string  { return "" }

func TestToSnakeCase(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"CamelCase", "camel_case"},
		{"XMLHttpRequest", "x_m_l_http_request"},
		{"ID", "i_d"},
		{"UserID", "user_i_d"},
		{"firstName", "first_name"},
		{"lastName", "last_name"},
		{"simple", "simple"},
		{"", ""},
	}

	for _, test := range tests {
		result := toSnakeCase(test.input)
		assert.Equal(t, test.expected, result, "Input: %s", test.input)
	}
}

func TestValidateUsername(t *testing.T) {
	tests := []struct {
		username string
		valid    bool
		message  string
	}{
		{"validuser123", true, ""},
		{"user_name", true, ""},
		{"ab", false, "username must be at least 3 characters long"},
		{"a_very_long_username_that_exceeds_thirty_characters", false, "username must be at most 30 characters long"},
		{"user name", false, "username must contain only alphanumeric characters and underscores"},
		{"user@name", false, "username must contain only alphanumeric characters and underscores"},
		{"user-name", false, "username must contain only alphanumeric characters and underscores"},
		{"123", true, ""},
		{"_underscore", true, ""},
	}

	for _, test := range tests {
		err := ValidateUsername(test.username)
		if test.valid {
			assert.NoError(t, err, "Username: %s", test.username)
		} else {
			assert.Error(t, err, "Username: %s", test.username)
			assert.Contains(t, err.Error(), test.message, "Username: %s", test.username)
		}
	}
}

func TestValidatePassword(t *testing.T) {
	tests := []struct {
		password string
		valid    bool
		message  string
	}{
		{"ValidPass123", true, ""},
		{"short", false, "password must be at least 8 characters long"},
		{"nouppercase123", false, "password must contain at least one uppercase letter"},
		{"NOLOWERCASE123", false, "password must contain at least one lowercase letter"},
		{"NoDigitsHere", false, "password must contain at least one digit"},
		{"Perfect123", true, ""},
		{"AnotherGood1", true, ""},
	}

	for _, test := range tests {
		err := ValidatePassword(test.password)
		if test.valid {
			assert.NoError(t, err, "Password: %s", test.password)
		} else {
			assert.Error(t, err, "Password: %s", test.password)
			assert.Contains(t, err.Error(), test.message, "Password: %s", test.password)
		}
	}
}

func TestValidateEmail(t *testing.T) {
	tests := []struct {
		email string
		valid bool
	}{
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"invalid-email", false},
		{"@example.com", false},
		{"user@", false},
		{"user@.com", false},
		{"user@domain", false},
		{"", false},
		{"user <EMAIL>", false},
	}

	for _, test := range tests {
		err := ValidateEmail(test.email)
		if test.valid {
			assert.NoError(t, err, "Email: %s", test.email)
		} else {
			assert.Error(t, err, "Email: %s", test.email)
			assert.Contains(t, err.Error(), "invalid email format", "Email: %s", test.email)
		}
	}
}
