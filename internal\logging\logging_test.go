package logging

import (
	"bytes"
	"os"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLogLevel_String(t *testing.T) {
	tests := []struct {
		level    LogLevel
		expected string
	}{
		{DEBUG, "DEBUG"},
		{INFO, "INFO"},
		{WARN, "WARN"},
		{ERROR, "ERROR"},
		{FATAL, "FATAL"},
		{LogLevel(999), "UNKNOWN"},
	}

	for _, test := range tests {
		result := test.level.String()
		assert.Equal(t, test.expected, result, "Incorrect string for log level %d", test.level)
	}
}

func TestNewLogger(t *testing.T) {
	var buf bytes.Buffer
	logger := NewLogger(INFO, &buf)

	assert.NotNil(t, logger)
	assert.Equal(t, INFO, logger.level)
	assert.Equal(t, &buf, logger.output)
}

func TestLogger_SetLevel(t *testing.T) {
	var buf bytes.Buffer
	logger := NewLogger(INFO, &buf)

	logger.SetLevel(DEBUG)
	assert.Equal(t, DEBUG, logger.level)

	logger.SetLevel(ERROR)
	assert.Equal(t, ERROR, logger.level)
}

func TestLogger_SetOutput(t *testing.T) {
	var buf1, buf2 bytes.Buffer
	logger := NewLogger(INFO, &buf1)

	logger.SetOutput(&buf2)
	assert.Equal(t, &buf2, logger.output)
}

func TestLogger_LogLevels(t *testing.T) {
	var buf bytes.Buffer
	logger := NewLogger(DEBUG, &buf)

	// Test all log levels
	logger.Debug("debug message", map[string]interface{}{"key": "value"})
	logger.Info("info message", nil)
	logger.Warn("warn message", map[string]interface{}{"count": 42})
	logger.Error("error message", map[string]interface{}{"error": "test error"})

	output := buf.String()
	
	// Check that all messages are present
	assert.Contains(t, output, "[DEBUG]")
	assert.Contains(t, output, "debug message")
	assert.Contains(t, output, "[INFO]")
	assert.Contains(t, output, "info message")
	assert.Contains(t, output, "[WARN]")
	assert.Contains(t, output, "warn message")
	assert.Contains(t, output, "[ERROR]")
	assert.Contains(t, output, "error message")

	// Check that fields are included
	assert.Contains(t, output, "key=value")
	assert.Contains(t, output, "count=42")
	assert.Contains(t, output, "error=test error")
}

func TestLogger_LevelFiltering(t *testing.T) {
	var buf bytes.Buffer
	logger := NewLogger(WARN, &buf)

	// These should not appear (below WARN level)
	logger.Debug("debug message", nil)
	logger.Info("info message", nil)

	// These should appear (WARN level and above)
	logger.Warn("warn message", nil)
	logger.Error("error message", nil)

	output := buf.String()
	
	// Check that only WARN and ERROR messages are present
	assert.NotContains(t, output, "[DEBUG]")
	assert.NotContains(t, output, "debug message")
	assert.NotContains(t, output, "[INFO]")
	assert.NotContains(t, output, "info message")
	assert.Contains(t, output, "[WARN]")
	assert.Contains(t, output, "warn message")
	assert.Contains(t, output, "[ERROR]")
	assert.Contains(t, output, "error message")
}

func TestLogger_LogFormat(t *testing.T) {
	var buf bytes.Buffer
	logger := NewLogger(INFO, &buf)

	logger.Info("test message", map[string]interface{}{"key": "value"})

	output := buf.String()
	
	// Check log format components
	assert.Contains(t, output, "[INFO]")
	assert.Contains(t, output, "test message")
	assert.Contains(t, output, "key=value")
	assert.Contains(t, output, "logging_test.go:")
	
	// Check timestamp format (should contain T and Z for RFC3339)
	assert.Contains(t, output, "T")
	assert.Contains(t, output, "Z")
}

func TestGlobalLogger(t *testing.T) {
	// Test that global logger is initialized
	assert.NotNil(t, globalLogger)

	// Test global logging functions
	var buf bytes.Buffer
	originalOutput := globalLogger.output
	globalLogger.SetOutput(&buf)
	defer globalLogger.SetOutput(originalOutput)

	Debug("debug message", nil)
	Info("info message", nil)
	Warn("warn message", nil)
	Error("error message", nil)

	output := buf.String()
	
	// Since default level is INFO, DEBUG should not appear
	assert.NotContains(t, output, "[DEBUG]")
	assert.Contains(t, output, "[INFO]")
	assert.Contains(t, output, "[WARN]")
	assert.Contains(t, output, "[ERROR]")
}

func TestSetupLogging(t *testing.T) {
	// Test setup with stdout only
	err := SetupLogging(DEBUG, "")
	assert.NoError(t, err)
	assert.Equal(t, DEBUG, globalLogger.level)

	// Test setup with log file
	tempFile := "test.log"
	defer os.Remove(tempFile)

	err = SetupLogging(WARN, tempFile)
	assert.NoError(t, err)
	assert.Equal(t, WARN, globalLogger.level)

	// Test that log file was created
	_, err = os.Stat(tempFile)
	assert.NoError(t, err)
}

func TestSetupLogging_InvalidFile(t *testing.T) {
	// Test setup with invalid file path
	err := SetupLogging(INFO, "/invalid/path/test.log")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to open log file")
}

func TestLogger_WithFields(t *testing.T) {
	var buf bytes.Buffer
	logger := NewLogger(INFO, &buf)

	// Test with multiple fields
	fields := map[string]interface{}{
		"user_id":   123,
		"action":    "login",
		"ip":        "***********",
		"timestamp": "2023-01-01T00:00:00Z",
	}

	logger.Info("User logged in", fields)

	output := buf.String()
	
	// Check that all fields are present
	assert.Contains(t, output, "user_id=123")
	assert.Contains(t, output, "action=login")
	assert.Contains(t, output, "ip=***********")
	assert.Contains(t, output, "timestamp=2023-01-01T00:00:00Z")
}

func TestLogger_NilFields(t *testing.T) {
	var buf bytes.Buffer
	logger := NewLogger(INFO, &buf)

	// Test with nil fields
	logger.Info("test message", nil)

	output := buf.String()
	
	// Should not crash and should contain the message
	assert.Contains(t, output, "test message")
	assert.Contains(t, output, "[INFO]")
}

func TestLogger_EmptyFields(t *testing.T) {
	var buf bytes.Buffer
	logger := NewLogger(INFO, &buf)

	// Test with empty fields map
	logger.Info("test message", map[string]interface{}{})

	output := buf.String()
	
	// Should not crash and should contain the message
	assert.Contains(t, output, "test message")
	assert.Contains(t, output, "[INFO]")
}
