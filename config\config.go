package config

import (
	"log"
	"os"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the application
type Config struct {
	// Server Configuration
	ServerPort string
	JWTSecret  string

	// Database Configuration
	DatabaseType string // "sqlite", "supabase", "neon"
	DatabaseURL  string
	DatabasePath string

	// AI API Keys
	OpenAIApiKey     string
	DeepSeekApiKey   string
	XAIApiKey        string
	GeminiApiKey     string
	VolcengineApiKey string
	DefaultAIModel   string

	// R2 Storage Configuration
	R2AccessKey string
	R2SecretKey string
	R2Endpoint  string
	R2Bucket    string

	// MCP Configuration
	EnableMCP bool
	MCPPort   string
}

// LoadConfig loads the configuration from environment variables
func LoadConfig() Config {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	config := Config{
		// Server Configuration
		ServerPort: getEnv("SERVER_PORT", "8080"),
		JWTSecret:  getEnv("JWT_SECRET", "your-secret-key"),

		// Database Configuration
		DatabaseType: getEnv("DATABASE_TYPE", "sqlite"),
		DatabaseURL:  getEnv("DATABASE_URL", ""),
		DatabasePath: getEnv("DATABASE_PATH", "database.db"),

		// AI API Keys
		OpenAIApiKey:     getEnv("OPENAI_API_KEY", ""),
		DeepSeekApiKey:   getEnv("DEEPSEEK_API_KEY", ""),
		XAIApiKey:        getEnv("XAI_API_KEY", ""),
		GeminiApiKey:     getEnv("GEMINI_API_KEY", ""),
		VolcengineApiKey: getEnv("VOLCENGINE_API_KEY", ""),
		DefaultAIModel:   getEnv("DEFAULT_AI_MODEL", "openai"),

		// R2 Storage Configuration
		R2AccessKey: getEnv("R2_ACCESS_KEY", ""),
		R2SecretKey: getEnv("R2_SECRET_KEY", ""),
		R2Endpoint:  getEnv("R2_ENDPOINT", ""),
		R2Bucket:    getEnv("R2_BUCKET", ""),

		// MCP Configuration
		EnableMCP: getEnvBool("ENABLE_MCP", false),
		MCPPort:   getEnv("MCP_PORT", "8081"),
	}

	return config
}

// getEnv reads an environment variable or returns a default value
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// getEnvBool reads an environment variable as a boolean or returns a default value
func getEnvBool(key string, defaultValue bool) bool {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}

	// Check for true values
	if value == "1" || value == "true" || value == "TRUE" || value == "True" || value == "yes" || value == "YES" || value == "Y" || value == "y" {
		return true
	}

	// Check for false values
	if value == "0" || value == "false" || value == "FALSE" || value == "False" || value == "no" || value == "NO" || value == "N" || value == "n" {
		return false
	}

	// If not recognized, return default
	return defaultValue
}
