package handlers

import (
	"github.com/gin-ai-backend/internal/ai"
	"github.com/gin-ai-backend/internal/ai/mcp"
	"github.com/gin-ai-backend/internal/logging"
)

// MCPHandler handles MCP server operations
type <PERSON><PERSON><PERSON>and<PERSON> struct {
	mcpServer *mcp.MCPServer
}

// NewMCPHandler creates a new MCP handler
func NewMCPHandler(aiClient *ai.Client) *MCPHandler {
	mcpServer := mcp.NewMCPServer(aiClient)

	return &MCPHandler{
		mcpServer: mcpServer,
	}
}

// StartMCPServer starts the MCP server
func (h *MCPHandler) StartMCPServer() {
	// Start the MCP server in a goroutine
	go func() {
		if err := h.mcpServer.Start(); err != nil {
			logging.Error("Failed to start MCP server", map[string]interface{}{
				"error": err.<PERSON><PERSON>r(),
			})
		}
	}()
}
