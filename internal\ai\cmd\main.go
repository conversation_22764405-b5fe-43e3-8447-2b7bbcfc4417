package main

import (
	"fmt"
	"log"
	"os"

	"github.com/gin-ai-backend/internal/ai/deepseek"
	"github.com/gin-ai-backend/internal/ai/gemini"
	"github.com/gin-ai-backend/internal/ai/volcengine"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	err := godotenv.Load()
	if err != nil {
		log.Println("Warning: Unable to load .env file, using system environment variables")
	}

	// Check command line arguments
	if len(os.Args) > 1 {
		provider := os.Args[1]
		runExample(provider)
	} else {
		fmt.Println("Please specify an AI provider example to run: gemini, deepseek, volcengine")
		fmt.Println("Usage: go run cmd/main.go [provider]")
		fmt.Println("Example: go run cmd/main.go gemini")
	}
}

func runExample(provider string) {
	switch provider {
	case "gemini":
		fmt.Println("Running Google Gemini AI example...")
		runGeminiExample()
	case "deepseek":
		fmt.Println("Running DeepSeek AI example...")
		runDeepSeekExample()
	case "volcengine":
		fmt.Println("Running Volcengine AI example...")
		runVolcengineExample()
	default:
		fmt.Printf("Unknown AI provider: %s\n", provider)
		fmt.Println("Supported providers: gemini, deepseek, volcengine")
	}
}

func runGeminiExample() {
	// Check API key
	apiKey := os.Getenv("GEMINI_API_KEY")
	if apiKey == "" {
		fmt.Println("Error: GEMINI_API_KEY environment variable not set")
		fmt.Println("Please add your Google Gemini API key to the .env file")
		return
	}

	// Run text generation example
	fmt.Println("\n1. Text Generation Example:")
	result, err := gemini.GenerateText(
		"Explain the basic principles of quantum computing in simple terms",
		&gemini.GenerateTextOptions{
			Temperature:     0.7,
			MaxOutputTokens: 1000,
			LogPrefix:       "[GeminiExample]",
		},
	)

	if err != nil {
		fmt.Printf("Error generating text: %v\n", err)
	} else {
		fmt.Println("Generation complete!")
		fmt.Printf("Generated text: %s\n", result)
	}

	// Run streaming text generation example
	fmt.Println("\n2. Streaming Text Generation Example:")
	err = gemini.StreamText(
		"Write a short poem about artificial intelligence",
		func(text string, done bool) {
			if text != "" {
				fmt.Print(text)
			}
			if done {
				fmt.Println("\n\nGeneration complete!")
			}
		},
		&gemini.GenerateTextOptions{
			Temperature:     0.9,
			MaxOutputTokens: 1000,
			LogPrefix:       "[GeminiStreamExample]",
		},
	)
	if err != nil {
		fmt.Printf("Error streaming text generation: %v\n", err)
	}

	fmt.Println("\nExample run complete")
}

func runDeepSeekExample() {
	// Check API key
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		fmt.Println("Error: DEEPSEEK_API_KEY environment variable not set")
		fmt.Println("Please add your DeepSeek API key to the .env file")
		return
	}

	// Run text generation example
	fmt.Println("\n1. Text Generation Example:")
	result, err := deepseek.GenerateText(
		"Explain the basic principles of quantum computing in simple terms",
		&deepseek.GenerateTextOptions{
			LogPrefix: "[DeepSeekExample]",
		},
	)

	if err != nil {
		fmt.Printf("Error generating text: %v\n", err)
	} else {
		fmt.Println("Generation complete!")
		fmt.Printf("Generated text: %s\n", result)
	}

	fmt.Println("\nExample run complete")
}

func runVolcengineExample() {
	// Check API key
	apiKey := os.Getenv("VOLCENGINE_API_KEY")
	if apiKey == "" {
		fmt.Println("Error: VOLCENGINE_API_KEY environment variable not set")
		fmt.Println("Please add your Volcengine API key to the .env file")
		return
	}

	// Run text generation example
	fmt.Println("\n1. Text Generation Example:")
	systemPrompt := "You are a professional AI assistant providing useful, safe, and accurate information."
	userPrompt := "Explain the basic principles of quantum computing in simple terms"

	result, err := volcengine.GenerateText(
		systemPrompt,
		userPrompt,
		&volcengine.GenerateTextOptions{
			Temperature: 0.7,
			MaxTokens:   1000,
			LogPrefix:   "[VolcengineExample]",
		},
	)

	if err != nil {
		fmt.Printf("Error generating text: %v\n", err)
	} else {
		fmt.Println("Generation complete!")
		fmt.Printf("Generated text: %s\n", result)
	}

	fmt.Println("\nExample run complete")
}
