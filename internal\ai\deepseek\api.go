package deepseek

// GenerateText generates text using the DeepSeek API
//
// This is the main public API function for text generation with DeepSeek.
// It uses the singleton instance of DeepSeekService to handle the request.
//
// Parameters:
//   - prompt: The user query or input text
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - The generated text response
//   - Any error that occurred during the process
func GenerateText(prompt string, options *GenerateTextOptions) (string, error) {
	return GetDeepSeekService().GenerateText(prompt, options)
}
