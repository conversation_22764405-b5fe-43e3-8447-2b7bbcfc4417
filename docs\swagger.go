package docs

// @title Gin AI Backend API
// @version 1.0
// @description A RESTful API backend service built with Go and the Gin framework, designed specifically for AI functionality.
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.example.com/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /
// @schemes http https
