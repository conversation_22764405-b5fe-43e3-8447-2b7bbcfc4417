package volcengine

// ResourcePrompt contains metadata for resource prompts used in the application
type ResourcePrompt struct {
	// ID is the unique identifier for the prompt
	ID string `json:"id"`
	// Title is the display name of the prompt
	Title string `json:"title"`
	// Description provides details about the prompt's purpose and usage
	Description string `json:"description"`
	// Categories classifies the prompt into different groups
	Categories []string `json:"categories"`
	// Tags are keywords associated with the prompt for searching and filtering
	Tags []string `json:"tags"`
	// ImagePrompt is the specific prompt text for image generation
	ImagePrompt string `json:"imagePrompt,omitempty"`
	// AudioPrompt is the specific prompt text for audio generation
	AudioPrompt string `json:"audioPrompt,omitempty"`
	// ChineseAudioPrompt is the Chinese version of the audio prompt
	ChineseAudioPrompt string `json:"chineseAudioPrompt,omitempty"`
}

// VolcengineMessage represents a message in the Volcengine API communication
type VolcengineMessage struct {
	// Role defines the message sender (system, user, assistant)
	Role string `json:"role"`
	// Content contains the actual message text
	Content string `json:"content"`
}

// VolcengineRequestBody represents the request body structure for the Volcengine API
type VolcengineRequestBody struct {
	// Model specifies which AI model to use
	Model string `json:"model"`
	// Messages contains the conversation history
	Messages []VolcengineMessage `json:"messages"`
	// MaxTokens limits the maximum number of tokens in the response
	MaxTokens int `json:"max_tokens,omitempty"`
	// Temperature controls randomness (0-1, higher = more random)
	Temperature float64 `json:"temperature,omitempty"`
	// TopP controls diversity via nucleus sampling
	TopP float64 `json:"top_p,omitempty"`
	// FrequencyPenalty reduces repetition of token sequences
	FrequencyPenalty float64 `json:"frequency_penalty,omitempty"`
	// PresencePenalty reduces repetition of topics
	PresencePenalty float64 `json:"presence_penalty,omitempty"`
}

// VolcengineChoice represents a single response option in the Volcengine API response
type VolcengineChoice struct {
	// Index is the position of this choice in the array of choices
	Index int `json:"index"`
	// Message contains the response content
	Message VolcengineMessage `json:"message"`
	// FinishReason indicates why the model stopped generating text
	FinishReason string `json:"finish_reason"`
}

// VolcengineResponseBody represents the complete response structure from the Volcengine API
type VolcengineResponseBody struct {
	// ID is the unique identifier for this response
	ID string `json:"id"`
	// Object indicates the type of object returned
	Object string `json:"object"`
	// Created is the Unix timestamp when the response was generated
	Created int64 `json:"created"`
	// Choices contains the array of generated responses
	Choices []VolcengineChoice `json:"choices"`
}

// GenerateTextOptions represents configuration options for text generation requests
type GenerateTextOptions struct {
	// Temperature controls randomness (0-1, higher = more random)
	Temperature float64
	// MaxTokens limits the maximum number of tokens in the response
	MaxTokens int
	// TopP controls diversity via nucleus sampling
	TopP float64
	// FrequencyPenalty reduces repetition of token sequences
	FrequencyPenalty float64
	// PresencePenalty reduces repetition of topics
	PresencePenalty float64
	// LogPrefix is prepended to log messages for this request
	LogPrefix string
}
