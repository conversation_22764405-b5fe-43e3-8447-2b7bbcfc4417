# Build stage
FROM golang:1.24-alpine AS builder
WORKDIR /app

# Build arguments for version information
ARG VERSION=dev
ARG BUILD_DATE=unknown

# Copy go mod files
COPY go.mod go.sum* ./
# Download dependencies and update go.sum
RUN go mod download
RUN go mod tidy
# Copy source code
COPY . .
# Ensure all dependencies are properly resolved
RUN go mod tidy
# Build the application with verbose output to see any issues
RUN CGO_ENABLED=0 GOOS=linux go build -v -o /gin-ai-backend ./cmd/main.go

# Final stage - Using Debian for media processing capabilities
FROM debian:bullseye-slim

# Pass build arguments to this stage
ARG VERSION
ARG BUILD_DATE

# Add labels for better image identification and tracking
LABEL org.opencontainers.image.created=$BUILD_DATE
LABEL org.opencontainers.image.version=$VERSION
LABEL org.opencontainers.image.description="Gin AI Backend Service"
LABEL org.opencontainers.image.source="https://github.com/dom-liu/gin-ai-backend"
LABEL maintainer="Dom liu <<EMAIL>>"

# Install FFmpeg and ImageMagick for media processing
RUN apt-get update && apt-get install -y --no-install-recommends \
    ffmpeg \
    imagemagick \
    ca-certificates \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Configure ImageMagick policy to allow PDF operations if needed
RUN if [ -f /etc/ImageMagick-6/policy.xml ]; then \
    sed -i 's/rights="none" pattern="PDF"/rights="read|write" pattern="PDF"/' /etc/ImageMagick-6/policy.xml; \
    fi

# Create a non-root user for enhanced security
RUN groupadd -r appuser && useradd -r -g appuser appuser
USER appuser
WORKDIR /app

# Copy the binary from builder
COPY --from=builder /gin-ai-backend /app/
COPY --from=builder /app/.env* /app/

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD ["/app/gin-ai-backend", "healthcheck"] || exit 1

# Expose the application port and MCP port
EXPOSE 8080 8081

# Run the binary
# To enable MCP, set ENABLE_MCP=true when running the container
CMD ["/app/gin-ai-backend"]
