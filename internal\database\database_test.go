package database

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestDatabase(t *testing.T) {
	// Add test cases for Database logic here
}

func TestInitializeSQLite(t *testing.T) {
	dbPath := ":memory:"
	err := Initialize("sqlite", "", dbPath)
	assert.NoError(t, err)
	assert.NotNil(t, DB)
	assert.IsType(t, &gorm.DB{}, DB)
}

func TestInitializeUnsupportedDB(t *testing.T) {
	err := Initialize("unsupported", "", "")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unsupported database type")
}
