package logging

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"time"
)

// LogLevel represents the severity of a log message
type LogLevel int

const (
	// DEBUG level for detailed information
	DEBUG LogLevel = iota
	// INFO level for general operational information
	INFO
	// WARN level for warning messages
	WARN
	// ERROR level for error messages
	ERROR
	// FATAL level for fatal error messages
	FATAL
)

// String returns the string representation of a log level
func (l LogLevel) String() string {
	switch l {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	case FATAL:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// Logger is a structured logger
type Logger struct {
	level  LogLevel
	output io.Writer
}

// NewLogger creates a new logger
func NewLogger(level LogLevel, output io.Writer) *Logger {
	return &Logger{
		level:  level,
		output: output,
	}
}

// SetLevel sets the log level
func (l *Logger) SetLevel(level LogLevel) {
	l.level = level
}

// SetOutput sets the output writer
func (l *Logger) SetOutput(output io.Writer) {
	l.output = output
}

// log logs a message at the specified level
func (l *Logger) log(level LogLevel, message string, fields map[string]interface{}) {
	if level < l.level {
		return
	}

	// Get caller information
	_, file, line, ok := runtime.Caller(2)
	if !ok {
		file = "unknown"
		line = 0
	}
	file = filepath.Base(file)

	// Format time
	timestamp := time.Now().Format(time.RFC3339)

	// Format fields
	fieldsStr := ""
	for k, v := range fields {
		fieldsStr += fmt.Sprintf(" %s=%v", k, v)
	}

	// Format log message
	logMessage := fmt.Sprintf("[%s] %s %s:%d %s%s\n", level.String(), timestamp, file, line, message, fieldsStr)

	// Write log message
	_, _ = l.output.Write([]byte(logMessage))

	// If level is FATAL, exit the program
	if level == FATAL {
		os.Exit(1)
	}
}

// Debug logs a debug message
func (l *Logger) Debug(message string, fields map[string]interface{}) {
	l.log(DEBUG, message, fields)
}

// Info logs an info message
func (l *Logger) Info(message string, fields map[string]interface{}) {
	l.log(INFO, message, fields)
}

// Warn logs a warning message
func (l *Logger) Warn(message string, fields map[string]interface{}) {
	l.log(WARN, message, fields)
}

// Error logs an error message
func (l *Logger) Error(message string, fields map[string]interface{}) {
	l.log(ERROR, message, fields)
}

// Fatal logs a fatal message and exits the program
func (l *Logger) Fatal(message string, fields map[string]interface{}) {
	l.log(FATAL, message, fields)
}

// Global logger instance
var globalLogger *Logger

// Initialize the global logger
func init() {
	// Default to INFO level and stdout
	globalLogger = NewLogger(INFO, os.Stdout)
}

// SetupLogging sets up the global logger
func SetupLogging(level LogLevel, logFile string) error {
	var output io.Writer = os.Stdout

	// If a log file is specified, use it
	if logFile != "" {
		file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0o666)
		if err != nil {
			return fmt.Errorf("failed to open log file: %w", err)
		}
		output = io.MultiWriter(os.Stdout, file)
	}

	globalLogger.SetLevel(level)
	globalLogger.SetOutput(output)

	// Replace the standard logger
	log.SetOutput(output)

	return nil
}

// Debug logs a debug message using the global logger
func Debug(message string, fields map[string]interface{}) {
	globalLogger.Debug(message, fields)
}

// Info logs an info message using the global logger
func Info(message string, fields map[string]interface{}) {
	globalLogger.Info(message, fields)
}

// Warn logs a warning message using the global logger
func Warn(message string, fields map[string]interface{}) {
	globalLogger.Warn(message, fields)
}

// Error logs an error message using the global logger
func Error(message string, fields map[string]interface{}) {
	globalLogger.Error(message, fields)
}

// Fatal logs a fatal message and exits the program using the global logger
func Fatal(message string, fields map[string]interface{}) {
	globalLogger.Fatal(message, fields)
}
