package main

import (
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gin-ai-backend/config"
	"github.com/gin-ai-backend/docs"
	"github.com/gin-ai-backend/internal/database"
	"github.com/gin-ai-backend/internal/handlers"
	"github.com/gin-ai-backend/internal/logging"
	"github.com/gin-ai-backend/internal/middleware"
	"github.com/gin-ai-backend/internal/storage"
	"github.com/gin-ai-backend/internal/version"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func main() {
	// 支持健康检查的命令行参数处理
	if len(os.Args) > 1 && os.Args[1] == "healthcheck" {
		runHealthCheck()
		return
	}

	// Initialize version information
	version.Initialize()

	// Load configuration
	cfg := config.LoadConfig()

	// Setup logging
	logLevel := logging.INFO
	if gin.Mode() == gin.DebugMode {
		logLevel = logging.DEBUG
	}

	if err := logging.SetupLogging(logLevel, "app.log"); err != nil {
		log.Fatalf("Failed to setup logging: %v", err)
	}

	logging.Info("Starting Gin AI Backend", map[string]interface{}{
		"version": version.GetVersion(),
		"mode":    gin.Mode(),
	})

	// Initialize database
	if err := database.Initialize(cfg.DatabaseType, cfg.DatabaseURL, cfg.DatabasePath); err != nil {
		logging.Fatal("Failed to initialize database", map[string]interface{}{
			"error": err.Error(),
			"type":  cfg.DatabaseType,
		})
	}

	logging.Info("Database initialized", map[string]interface{}{
		"type": cfg.DatabaseType,
	})

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(cfg.JWTSecret)
	aiHandler := handlers.NewAIHandler(
		cfg.OpenAIApiKey,
		cfg.DeepSeekApiKey,
		cfg.XAIApiKey,
		cfg.XAIApiKey, // Using XAIApiKey for GrokApiKey as they are the same
		cfg.GeminiApiKey,
		cfg.VolcengineApiKey,
		cfg.DefaultAIModel,
	)

	// Initialize MCP handler if enabled
	if cfg.EnableMCP {
		mcpHandler := handlers.NewMCPHandler(aiHandler.GetAIClient())
		mcpHandler.StartMCPServer()
		logging.Info("MCP server initialized", map[string]interface{}{
			"port": cfg.MCPPort,
		})
	}

	// Initialize file handler
	var fileHandler *handlers.FileHandler
	var mediaHandler *handlers.MediaHandler
	var r2Client storage.R2ClientInterface

	// Create R2 client for both file and media handlers
	r2Client, err := storage.NewR2Client(
		cfg.R2AccessKey,
		cfg.R2SecretKey,
		cfg.R2Endpoint,
		cfg.R2Bucket,
	)

	if err != nil {
		logging.Warn("R2 client initialization failed", map[string]interface{}{
			"error": err.Error(),
		})
		logging.Warn("File upload and media processing functionality will be disabled", nil)
	} else {
		// Initialize file handler
		fileHandler = &handlers.FileHandler{
			R2Client: r2Client,
		}

		// Initialize media handler with temp directory
		tempDir := "./temp/media"
		mediaHandler, err = handlers.NewMediaHandler(r2Client, tempDir)
		if err != nil {
			logging.Warn("Media handler initialization failed", map[string]interface{}{
				"error": err.Error(),
			})
			logging.Warn("Media processing functionality will be disabled", nil)
		} else {
			logging.Info("Media handler initialized", map[string]interface{}{
				"tempDir": tempDir,
			})
		}

		logging.Info("File handler initialized", map[string]interface{}{
			"endpoint": cfg.R2Endpoint,
			"bucket":   cfg.R2Bucket,
		})
	}

	// Swagger documentation setup
	docs.SwaggerInfo.BasePath = "/"

	// Set up router with custom error handling and logging
	router := gin.New()
	router.Use(middleware.LoggingMiddleware()) // Use our custom logging middleware
	router.Use(middleware.ErrorHandlerMiddleware())

	// Set custom handlers for 404 and 405 errors
	router.NoRoute(middleware.NotFoundHandler)
	router.NoMethod(middleware.MethodNotAllowedHandler)

	// Add security middlewares
	router.Use(middleware.SecurityHeadersMiddleware())
	router.Use(middleware.CORSMiddleware())

	// Public routes
	// @Summary Root endpoint
	// @Description Get basic information about the API
	// @Tags general
	// @Produce json
	// @Success 200 {object} gin.H
	// @Router / [get]
	router.GET("/", func(c *gin.Context) {
		features := []string{
			"Multiple AI providers (OpenAI, DeepSeek, XAI, Google Gemini)",
			"Multiple database support (SQLite, Supabase, Neon)",
			"JWT Authentication",
			"File uploads with Cloudflare R2",
		}

		// Add MCP feature if enabled
		if cfg.EnableMCP {
			features = append(features, "Model Context Protocol (MCP) support")
		}

		c.JSON(200, gin.H{
			"message":  "Welcome to Gin AI Backend",
			"version":  version.GetVersion(),
			"features": features,
		})
	})

	// 健康检查路由 - 不需要认证
	// @Summary Health check endpoint
	// @Description Check if the API is healthy
	// @Tags general
	// @Produce json
	// @Success 200 {object} gin.H
	// @Router /health [get]
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "healthy",
			"version": version.GetVersion(),
		})
	})

	// Swagger documentation endpoint
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// Auth routes
	auth := router.Group("/auth")
	// Rate limit to 20 requests per minute for auth routes
	auth.Use(middleware.RateLimitMiddleware(time.Minute, 20))
	{
		auth.POST("/register", authHandler.Register)
		auth.POST("/login", authHandler.Login)
		auth.GET("/profile", middleware.JWTAuthMiddleware(cfg.JWTSecret), authHandler.GetProfile)
	}

	// API routes (protected)
	api := router.Group("/api")
	api.Use(
		middleware.JWTAuthMiddleware(cfg.JWTSecret),
		// Rate limit to 100 requests per minute
		middleware.RateLimitMiddleware(time.Minute, 100),
	)
	{
		// AI routes
		api.POST("/ai/generate", aiHandler.GenerateResponse)

		// AI providers info route
		// @Summary Get available AI providers
		// @Description Get information about available AI providers
		// @Tags ai
		// @Produce json
		// @Security ApiKeyAuth
		// @Success 200 {object} gin.H
		// @Router /api/ai/providers [get]
		api.GET("/ai/providers", func(c *gin.Context) {
			providers := map[string]bool{
				"openai":     cfg.OpenAIApiKey != "",
				"deepseek":   cfg.DeepSeekApiKey != "",
				"xai":        cfg.XAIApiKey != "",
				"grok":       cfg.XAIApiKey != "", // Using XAIApiKey for GrokApiKey
				"gemini":     cfg.GeminiApiKey != "",
				"volcengine": cfg.VolcengineApiKey != "",
			}

			c.JSON(200, gin.H{
				"default":   cfg.DefaultAIModel,
				"available": providers,
			})
		})

		// MCP info route
		// @Summary Get MCP information
		// @Description Get information about MCP configuration
		// @Tags ai
		// @Produce json
		// @Security ApiKeyAuth
		// @Success 200 {object} gin.H
		// @Router /api/ai/mcp [get]
		api.GET("/ai/mcp", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"enabled": cfg.EnableMCP,
				"port":    cfg.MCPPort,
				"info":    "Model Context Protocol (MCP) allows AI assistants to connect to external data sources and tools",
			})
		})

		// File routes (only if file handler is initialized)
		if fileHandler != nil {
			api.POST("/files/upload", fileHandler.UploadFile)
			api.GET("/files/:fileName", fileHandler.GetFile)
			api.DELETE("/files/:fileName", fileHandler.DeleteFile)

			// Media processing routes (only if media handler is initialized)
			if mediaHandler != nil {
				// Image conversion route
				api.GET("/media/convert/image/:fileName", mediaHandler.ConvertImage)

				// Image resizing route
				api.GET("/media/resize/image/:fileName", mediaHandler.ResizeImage)

				// Audio conversion route
				api.GET("/media/convert/audio/:fileName", mediaHandler.ConvertAudio)
			}
		}
	}

	// Admin routes
	admin := router.Group("/admin")
	admin.Use(middleware.JWTAuthMiddleware(cfg.JWTSecret), middleware.AdminMiddleware())
	{
		// Add admin routes here
		// @Summary Admin area
		// @Description Access the admin area
		// @Tags admin
		// @Produce json
		// @Security ApiKeyAuth
		// @Success 200 {object} gin.H
		// @Failure 401 {object} gin.H
		// @Failure 403 {object} gin.H
		// @Router /admin/ [get]
		admin.GET("/", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"message": "Admin area",
			})
		})

		// Users list route with pagination
		admin.GET("/users", authHandler.ListUsers)

		// Database info route
		// @Summary Database information
		// @Description Get information about the database configuration
		// @Tags admin
		// @Produce json
		// @Security ApiKeyAuth
		// @Success 200 {object} gin.H
		// @Failure 401 {object} gin.H
		// @Failure 403 {object} gin.H
		// @Router /admin/database/info [get]
		admin.GET("/database/info", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"type": cfg.DatabaseType,
				"path": cfg.DatabasePath,
				"url":  maskConnectionString(cfg.DatabaseURL),
			})
		})
	}

	// Start server
	logging.Info("Server starting", map[string]interface{}{
		"port":         cfg.ServerPort,
		"databaseType": cfg.DatabaseType,
	})

	if err := router.Run(":" + cfg.ServerPort); err != nil {
		logging.Fatal("Failed to start server", map[string]interface{}{
			"error": err.Error(),
			"port":  cfg.ServerPort,
		})
	}
}

// maskConnectionString masks sensitive information in connection strings
func maskConnectionString(connStr string) string {
	if connStr == "" {
		return ""
	}
	return "********" // For security, we don't expose the connection string
}

// runHealthCheck 执行健康检查，供 Docker HEALTHCHECK 使用
func runHealthCheck() {
	// Initialize version information
	version.Initialize()

	// Setup minimal logging for health check
	if err := logging.SetupLogging(logging.INFO, ""); err != nil {
		log.Fatalf("Failed to setup logging: %v", err)
	}

	// 默认使用 localhost:8080，除非在环境变量中指定了其他端口
	port := os.Getenv("SERVER_PORT")
	if port == "" {
		port = "8080"
	}

	logging.Info("Running health check", map[string]interface{}{
		"port": port,
	})

	// 向健康检查端点发送 GET 请求
	resp, err := http.Get("http://localhost:" + port + "/health")
	if err != nil {
		logging.Error("Health check failed", map[string]interface{}{
			"error": err.Error(),
		})
		os.Exit(1)
	}
	defer resp.Body.Close()

	// 检查响应状态码，非 200 状态码表示不健康
	if resp.StatusCode != http.StatusOK {
		logging.Error("Health check returned non-200 status code", map[string]interface{}{
			"statusCode": resp.StatusCode,
		})
		os.Exit(1)
	}

	logging.Info("Health check passed", nil)
	os.Exit(0)
}
