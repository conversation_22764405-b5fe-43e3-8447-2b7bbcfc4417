package grok

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// GrokService is a client for the Grok AI service
type GrokService struct {
	// APIKey is the authentication key for the Grok API
	APIKey string
	// BaseURL is the endpoint URL for the Grok API
	BaseURL string
	// DefaultModel is the identifier for the default AI model to use
	DefaultModel string
}

// NewGrokService creates a new Grok service client
// It loads configuration from environment variables and sets default values if needed
func NewGrokService() *GrokService {
	// Try to load .env file
	_ = godotenv.Load()

	apiKey := os.Getenv("XAI_API_KEY")
	baseURL := os.Getenv("XAI_API_URL")
	defaultModel := os.Getenv("XAI_MODEL_ID")

	// Set default values
	if baseURL == "" {
		baseURL = "https://api.x.ai/v1"
	}
	if defaultModel == "" {
		defaultModel = "grok-3-beta"
	}

	log.Println("Initializing GrokService...")
	service := &GrokService{
		APIKey:       apiKey,
		BaseURL:      baseURL,
		DefaultModel: defaultModel,
	}

	if apiKey == "" {
		log.Println("Warning: XAI_API_KEY not set in environment variables")
	} else {
		log.Println("GrokService initialized with API key")
	}

	log.Printf("Using Grok API URL: %s\n", baseURL)
	log.Printf("Using Grok default model: %s\n", defaultModel)

	return service
}

// GenerateText generates text using the Grok API
//
// Parameters:
//   - prompt: The user query or input text
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - The generated text response
//   - Any error that occurred during the process
func (s *GrokService) GenerateText(prompt string, options *GenerateTextOptions) (string, error) {
	if options == nil {
		options = &GenerateTextOptions{}
	}

	// Set default values
	model := options.Model
	if model == "" {
		model = s.DefaultModel
	}

	temperature := options.Temperature
	if temperature == 0 {
		temperature = 0.7
	}

	maxTokens := options.MaxTokens
	if maxTokens == 0 {
		maxTokens = 2048
	}

	topP := options.TopP
	if topP == 0 {
		topP = 0.95
	}

	logPrefix := options.LogPrefix
	if logPrefix == "" {
		logPrefix = "[Grok]"
	}

	if s.APIKey == "" {
		log.Printf("%s XAI_API_KEY not set, cannot generate content\n", logPrefix)
		return "", fmt.Errorf("XAI_API_KEY not set")
	}

	log.Printf("%s Calling Grok API...\n", logPrefix)
	log.Printf("%s Prompt length: %d\n", logPrefix, len(prompt))

	// Build messages
	messages := []GrokMessage{}

	// Add system prompt
	if options.SystemPrompt != "" {
		messages = append(messages, GrokMessage{
			Role:    "system",
			Content: options.SystemPrompt,
		})
	}

	// Add user prompt
	messages = append(messages, GrokMessage{
		Role:    "user",
		Content: prompt,
	})

	// Build request data
	requestData := GrokRequestBody{
		Model:       model,
		Messages:    messages,
		Temperature: temperature,
		MaxTokens:   maxTokens,
		TopP:        topP,
	}

	// Serialize request data
	requestJSON, err := json.Marshal(requestData)
	if err != nil {
		return "", fmt.Errorf("Failed to serialize request data: %w", err)
	}

	// Set request URL
	url := fmt.Sprintf("%s/chat/completions", s.BaseURL)

	startTime := time.Now()
	log.Printf("%s Sending request to Grok AI...\n", logPrefix)

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestJSON))
	if err != nil {
		return "", fmt.Errorf("Failed to create HTTP request: %w", err)
	}

	// Add request headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.APIKey)

	// Send request
	client := &http.Client{
		Timeout: 60 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("Failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	elapsedTime := time.Since(startTime).Milliseconds()
	log.Printf("%s Grok API response time: %dms, status code: %d\n", logPrefix, elapsedTime, resp.StatusCode)

	// Check response status code
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API returned non-200 status code: %d", resp.StatusCode)
	}

	// Parse response
	var responseBody GrokResponseBody
	if err := json.NewDecoder(resp.Body).Decode(&responseBody); err != nil {
		return "", fmt.Errorf("Failed to parse response: %w", err)
	}

	// Extract response content
	if len(responseBody.Choices) > 0 {
		content := responseBody.Choices[0].Message.Content
		log.Printf("%s Received Grok AI response, length: %d\n", logPrefix, len(content))
		return content, nil
	}

	log.Printf("%s Unexpected API response structure\n", logPrefix)
	return "", fmt.Errorf("No content in API response")
}

// StreamText generates text using the Grok API in streaming mode
//
// Parameters:
//   - prompt: The user query or input text
//   - callback: Function to call for each chunk of generated text
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - Any error that occurred during the process
func (s *GrokService) StreamText(prompt string, callback StreamTextCallback, options *GenerateTextOptions) error {
	if options == nil {
		options = &GenerateTextOptions{}
	}

	// Set default values
	model := options.Model
	if model == "" {
		model = s.DefaultModel
	}

	temperature := options.Temperature
	if temperature == 0 {
		temperature = 0.7
	}

	maxTokens := options.MaxTokens
	if maxTokens == 0 {
		maxTokens = 2048
	}

	topP := options.TopP
	if topP == 0 {
		topP = 0.95
	}

	logPrefix := options.LogPrefix
	if logPrefix == "" {
		logPrefix = "[Grok]"
	}

	if s.APIKey == "" {
		log.Printf("%s XAI_API_KEY not set, cannot generate content\n", logPrefix)
		return fmt.Errorf("XAI_API_KEY not set")
	}

	log.Printf("%s Calling Grok API in streaming mode...\n", logPrefix)
	log.Printf("%s Prompt length: %d\n", logPrefix, len(prompt))

	// Build messages
	messages := []GrokMessage{}

	// Add system prompt
	if options.SystemPrompt != "" {
		messages = append(messages, GrokMessage{
			Role:    "system",
			Content: options.SystemPrompt,
		})
	}

	// Add user prompt
	messages = append(messages, GrokMessage{
		Role:    "user",
		Content: prompt,
	})

	// Build request data
	requestData := GrokRequestBody{
		Model:       model,
		Messages:    messages,
		Temperature: temperature,
		MaxTokens:   maxTokens,
		TopP:        topP,
		Stream:      true,
	}

	// Serialize request data
	requestJSON, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("Failed to serialize request data: %w", err)
	}

	// Set request URL
	url := fmt.Sprintf("%s/chat/completions", s.BaseURL)

	startTime := time.Now()
	log.Printf("%s Sending streaming request to Grok AI...\n", logPrefix)

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestJSON))
	if err != nil {
		return fmt.Errorf("Failed to create HTTP request: %w", err)
	}

	// Add request headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.APIKey)
	req.Header.Set("Accept", "text/event-stream")

	// Send request
	client := &http.Client{
		Timeout: 120 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("Failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status code
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("API returned non-200 status code: %d", resp.StatusCode)
	}

	// Read streaming response
	reader := bufio.NewReader(resp.Body)
	fullText := ""

	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			break
		}

		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Skip non-data lines
		if !strings.HasPrefix(line, "data: ") {
			continue
		}

		// Extract data part
		data := strings.TrimPrefix(line, "data: ")
		if data == "[DONE]" {
			break
		}

		// Parse JSON
		var chunkData map[string]interface{}
		if err := json.Unmarshal([]byte(data), &chunkData); err != nil {
			continue
		}

		// Extract content
		choices, ok := chunkData["choices"].([]interface{})
		if !ok || len(choices) == 0 {
			continue
		}

		choice, ok := choices[0].(map[string]interface{})
		if !ok {
			continue
		}

		delta, ok := choice["delta"].(map[string]interface{})
		if !ok {
			continue
		}

		content, ok := delta["content"].(string)
		if !ok {
			continue
		}

		fullText += content
		callback(content, false)
	}

	elapsedTime := time.Since(startTime).Milliseconds()
	log.Printf("%s Grok API streaming response completed, time: %dms, total length: %d\n", logPrefix, elapsedTime, len(fullText))
	callback("", true)

	return nil
}

// CreateChatCompletion creates a chat completion using the Grok API
//
// Parameters:
//   - messages: Array of messages representing the conversation history
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - The complete response from the API
//   - Any error that occurred during the process
func (s *GrokService) CreateChatCompletion(messages []GrokMessage, options *GenerateTextOptions) (*GrokResponseBody, error) {
	if options == nil {
		options = &GenerateTextOptions{}
	}

	// Set default values
	model := options.Model
	if model == "" {
		model = s.DefaultModel
	}

	temperature := options.Temperature
	if temperature == 0 {
		temperature = 0.7
	}

	maxTokens := options.MaxTokens
	if maxTokens == 0 {
		maxTokens = 2048
	}

	topP := options.TopP
	if topP == 0 {
		topP = 0.95
	}

	if s.APIKey == "" {
		log.Println("[Grok] XAI_API_KEY not set, cannot generate content")
		return nil, fmt.Errorf("XAI_API_KEY not set")
	}

	log.Println("[Grok] Calling Grok API chat completion...")
	log.Printf("[Grok] Number of messages: %d\n", len(messages))

	// Build request data
	requestData := GrokRequestBody{
		Model:       model,
		Messages:    messages,
		Temperature: temperature,
		MaxTokens:   maxTokens,
		TopP:        topP,
	}

	// Serialize request data
	requestJSON, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("Failed to serialize request data: %w", err)
	}

	// Set request URL
	url := fmt.Sprintf("%s/chat/completions", s.BaseURL)

	startTime := time.Now()
	log.Println("[Grok] Sending chat completion request to Grok AI...")

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestJSON))
	if err != nil {
		return nil, fmt.Errorf("Failed to create HTTP request: %w", err)
	}

	// Add request headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.APIKey)

	// Send request
	client := &http.Client{
		Timeout: 60 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("Failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	elapsedTime := time.Since(startTime).Milliseconds()
	log.Printf("[Grok] Grok API chat completion response time: %dms, status code: %d\n", elapsedTime, resp.StatusCode)

	// Check response status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned non-200 status code: %d", resp.StatusCode)
	}

	// Parse response
	var responseBody GrokResponseBody
	if err := json.NewDecoder(resp.Body).Decode(&responseBody); err != nil {
		return nil, fmt.Errorf("Failed to parse response: %w", err)
	}

	return &responseBody, nil
}

// grokService is the singleton instance of GrokService
var grokService *GrokService

// GetGrokService returns the singleton instance of the Grok service
func GetGrokService() *GrokService {
	if grokService == nil {
		grokService = NewGrokService()
	}
	return grokService
}
