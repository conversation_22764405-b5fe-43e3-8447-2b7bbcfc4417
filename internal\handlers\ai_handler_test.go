package handlers

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-ai-backend/internal/ai"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestGenerateResponse(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.Default()

	// Create a properly initialized AIHandler with a mock client
	client := ai.NewClient("test-key", "test-key", "test-key", "test-key", "test-key", "test-key", "openai")

	// Override the GenerateResponse function with a mock
	client.GenerateResponse = func(prompt string, provider ...ai.Provider) (string, error) {
		return "This is a test response", nil
	}

	handler := &AIHandler{
		AIClient: client,
	}

	router.POST("/ai/response", func(c *gin.Context) {
		c.Set("username", "testuser") // Simulate authenticated user
		handler.GenerateResponse(c)
	})

	// Create a test request
	body := `{"prompt": "Hello, AI!"}`
	req, _ := http.NewRequest("POST", "/ai/response", strings.NewReader(body))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Validate response
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), "response")
}
