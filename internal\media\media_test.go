package media

import (
	"os"
	"os/exec"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetImageFormat(t *testing.T) {
	tests := []struct {
		filename string
		expected ImageFormat
	}{
		{"image.jpg", JPEG},
		{"image.jpeg", JPEG},
		{"image.png", PNG},
		{"image.gif", GIF},
		{"image.webp", WEBP},
		{"image.avif", AVIF},
		{"image.unknown", PNG}, // Default
	}

	for _, test := range tests {
		result := GetImageFormat(test.filename)
		assert.Equal(t, test.expected, result, "Incorrect format for %s", test.filename)
	}
}

func TestGetAudioFormat(t *testing.T) {
	tests := []struct {
		filename string
		expected AudioFormat
	}{
		{"audio.mp3", MP3},
		{"audio.wav", WAV},
		{"audio.ogg", OGG},
		{"audio.flac", FLAC},
		{"audio.aac", AAC},
		{"audio.unknown", MP3}, // Default
	}

	for _, test := range tests {
		result := GetAudioFormat(test.filename)
		assert.Equal(t, test.expected, result, "Incorrect format for %s", test.filename)
	}
}

func TestNewMediaProcessor(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := filepath.Join(os.TempDir(), "media_test")
	defer os.RemoveAll(tempDir)

	processor, err := NewMediaProcessor(tempDir)
	assert.NoError(t, err)
	assert.Equal(t, tempDir, processor.TempDir)

	// Check if directory was created
	_, err = os.Stat(tempDir)
	assert.NoError(t, err)
}

// Note: The following tests require FFmpeg and ImageMagick to be installed
// They will be skipped if the commands are not available

func TestConvertImageFormat(t *testing.T) {
	// Skip if ImageMagick is not installed
	_, err := exec.LookPath("convert")
	if err != nil {
		t.Skip("ImageMagick not installed, skipping test")
	}

	// Create a temporary directory for testing
	tempDir := filepath.Join(os.TempDir(), "media_test")
	defer os.RemoveAll(tempDir)

	processor, err := NewMediaProcessor(tempDir)
	assert.NoError(t, err)

	// Create a simple test image (1x1 pixel PNG)
	testImageData := []byte{
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
		0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
		0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
		0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0xCF, 0xC0, 0x00,
		0x00, 0x03, 0x01, 0x01, 0x00, 0x18, 0xDD, 0x8D, 0xB0, 0x00, 0x00, 0x00,
		0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82,
	}

	// Test conversion from PNG to JPEG
	convertedData, err := processor.ConvertImageFormat(testImageData, PNG, JPEG)
	if err != nil {
		t.Logf("Conversion error: %v", err)
		t.Skip("Image conversion failed, possibly due to ImageMagick configuration")
	}

	assert.NotNil(t, convertedData)
	assert.True(t, len(convertedData) > 0)
}

func TestResizeImage(t *testing.T) {
	// Skip if ImageMagick is not installed
	_, err := exec.LookPath("convert")
	if err != nil {
		t.Skip("ImageMagick not installed, skipping test")
	}

	// Create a temporary directory for testing
	tempDir := filepath.Join(os.TempDir(), "media_test")
	defer os.RemoveAll(tempDir)

	processor, err := NewMediaProcessor(tempDir)
	assert.NoError(t, err)

	// Create a simple test image (2x2 pixel PNG)
	testImageData := []byte{
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
		0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x02,
		0x08, 0x02, 0x00, 0x00, 0x00, 0xFD, 0xD4, 0x9A, 0x73, 0x00, 0x00, 0x00,
		0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0xCF, 0xC0, 0x00,
		0x00, 0x03, 0x01, 0x01, 0x00, 0x18, 0xDD, 0x8D, 0xB0, 0x00, 0x00, 0x00,
		0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82,
	}

	// Test resizing
	resizeOptions := ResizeOptions{
		Width:    4,
		Height:   4,
		Preserve: true,
	}

	resizedData, err := processor.ResizeImage(testImageData, PNG, resizeOptions)
	if err != nil {
		t.Logf("Resize error: %v", err)
		t.Skip("Image resizing failed, possibly due to ImageMagick configuration")
	}

	assert.NotNil(t, resizedData)
	assert.True(t, len(resizedData) > 0)
}

func TestConvertAudioFormat(t *testing.T) {
	// Skip if FFmpeg is not installed
	_, err := exec.LookPath("ffmpeg")
	if err != nil {
		t.Skip("FFmpeg not installed, skipping test")
	}

	// Create a temporary directory for testing
	tempDir := filepath.Join(os.TempDir(), "media_test")
	defer os.RemoveAll(tempDir)

	processor, err := NewMediaProcessor(tempDir)
	assert.NoError(t, err)

	// Create a simple test WAV file (empty header)
	testAudioData := []byte{
		0x52, 0x49, 0x46, 0x46, // "RIFF"
		0x24, 0x00, 0x00, 0x00, // Chunk size
		0x57, 0x41, 0x56, 0x45, // "WAVE"
		0x66, 0x6D, 0x74, 0x20, // "fmt "
		0x10, 0x00, 0x00, 0x00, // Subchunk1 size
		0x01, 0x00, // Audio format (PCM)
		0x01, 0x00, // Num channels (Mono)
		0x44, 0xAC, 0x00, 0x00, // Sample rate (44100)
		0x88, 0x58, 0x01, 0x00, // Byte rate
		0x02, 0x00, // Block align
		0x10, 0x00, // Bits per sample
		0x64, 0x61, 0x74, 0x61, // "data"
		0x00, 0x00, 0x00, 0x00, // Subchunk2 size (0)
	}

	// Test conversion from WAV to MP3
	convertedData, err := processor.ConvertAudioFormat(testAudioData, WAV, MP3)
	if err != nil {
		t.Logf("Conversion error: %v", err)
		t.Skip("Audio conversion failed, possibly due to FFmpeg configuration or invalid test data")
	}

	assert.NotNil(t, convertedData)
	assert.True(t, len(convertedData) > 0)
}
