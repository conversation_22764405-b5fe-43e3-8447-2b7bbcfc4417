package version

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetVersion_Default(t *testing.T) {
	// Reset version to default
	Version = "0.0.0"
	
	version := GetVersion()
	assert.Equal(t, "0.0.0", version)
}

func TestGetVersion_CustomVersion(t *testing.T) {
	// Set custom version
	Version = "1.2.3"
	
	version := GetVersion()
	assert.Equal(t, "1.2.3", version)
	
	// Reset to default
	Version = "0.0.0"
}

func TestInitialize_WithEnvironmentVariable(t *testing.T) {
	// Save original version
	originalVersion := Version
	defer func() {
		Version = originalVersion
	}()
	
	// Set environment variable
	os.Setenv("VERSION", "2.1.0")
	defer os.Unsetenv("VERSION")
	
	// Initialize should read from environment
	Initialize()
	
	version := GetVersion()
	assert.Equal(t, "2.1.0", version)
}

func TestInitialize_WithoutEnvironmentVariable(t *testing.T) {
	// Save original version
	originalVersion := Version
	defer func() {
		Version = originalVersion
	}()
	
	// Set a custom version first
	Version = "1.0.0"
	
	// Ensure VERSION environment variable is not set
	os.Unsetenv("VERSION")
	
	// Initialize should not change the version
	Initialize()
	
	version := GetVersion()
	assert.Equal(t, "1.0.0", version)
}

func TestInitialize_EmptyEnvironmentVariable(t *testing.T) {
	// Save original version
	originalVersion := Version
	defer func() {
		Version = originalVersion
	}()
	
	// Set a custom version first
	Version = "1.0.0"
	
	// Set empty environment variable
	os.Setenv("VERSION", "")
	defer os.Unsetenv("VERSION")
	
	// Initialize should not change the version when env var is empty
	Initialize()
	
	version := GetVersion()
	assert.Equal(t, "1.0.0", version)
}

func TestInitialize_OverwriteVersion(t *testing.T) {
	// Save original version
	originalVersion := Version
	defer func() {
		Version = originalVersion
	}()
	
	// Set initial version
	Version = "1.0.0"
	
	// Set environment variable to different version
	os.Setenv("VERSION", "3.0.0")
	defer os.Unsetenv("VERSION")
	
	// Initialize should overwrite the version
	Initialize()
	
	version := GetVersion()
	assert.Equal(t, "3.0.0", version)
}

func TestVersionPersistence(t *testing.T) {
	// Save original version
	originalVersion := Version
	defer func() {
		Version = originalVersion
	}()
	
	// Set version
	Version = "1.5.2"
	
	// Multiple calls to GetVersion should return the same value
	version1 := GetVersion()
	version2 := GetVersion()
	version3 := GetVersion()
	
	assert.Equal(t, "1.5.2", version1)
	assert.Equal(t, "1.5.2", version2)
	assert.Equal(t, "1.5.2", version3)
	assert.Equal(t, version1, version2)
	assert.Equal(t, version2, version3)
}

func TestVersionFormats(t *testing.T) {
	// Save original version
	originalVersion := Version
	defer func() {
		Version = originalVersion
	}()
	
	// Test various version formats
	testVersions := []string{
		"1.0.0",
		"2.1.3",
		"0.0.1",
		"10.20.30",
		"1.0.0-alpha",
		"1.0.0-beta.1",
		"1.0.0-rc.1",
		"v1.2.3",
		"dev",
		"latest",
	}
	
	for _, testVersion := range testVersions {
		Version = testVersion
		result := GetVersion()
		assert.Equal(t, testVersion, result, "Version format: %s", testVersion)
	}
}

func TestConcurrentAccess(t *testing.T) {
	// Save original version
	originalVersion := Version
	defer func() {
		Version = originalVersion
	}()
	
	// Set version
	Version = "1.0.0"
	
	// Test concurrent access to GetVersion
	done := make(chan bool, 10)
	
	for i := 0; i < 10; i++ {
		go func() {
			version := GetVersion()
			assert.Equal(t, "1.0.0", version)
			done <- true
		}()
	}
	
	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}
}
