package middleware

import (
	"time"

	"github.com/gin-ai-backend/internal/logging"
	"github.com/gin-gonic/gin"
)

// LoggingMiddleware logs request and response details
func LoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Start timer
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		// Log request
		logging.Info("Request started", map[string]interface{}{
			"path":     path,
			"method":   method,
			"clientIP": c.ClientIP(),
		})

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)
		statusCode := c.Writer.Status()

		// Log fields based on status code
		fields := map[string]interface{}{
			"path":       path,
			"method":     method,
			"statusCode": statusCode,
			"latency":    latency.String(),
			"clientIP":   c.ClientIP(),
		}

		// Add error if present
		if len(c.Errors) > 0 {
			fields["error"] = c.Errors.String()
		}

		// Log based on status code
		if statusCode >= 500 {
			logging.Error("Request completed with server error", fields)
		} else if statusCode >= 400 {
			logging.Warn("Request completed with client error", fields)
		} else {
			logging.Info("Request completed successfully", fields)
		}
	}
}
