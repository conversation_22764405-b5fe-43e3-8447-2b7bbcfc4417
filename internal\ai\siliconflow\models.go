package siliconflow

// ImageGenerationConfig represents image generation configuration options
type ImageGenerationConfig struct {
	// Width is the width of the generated image in pixels
	Width int `json:"width"`
	// Height is the height of the generated image in pixels
	Height int `json:"height"`
	// Quality is the quality level of the generated image
	Quality string `json:"quality"`
	// Prompt is the text description of the image to generate
	Prompt string `json:"prompt"`
	// NegativePrompt specifies what should not appear in the generated image
	NegativePrompt string `json:"negative_prompt,omitempty"`
	// Seed is the random seed for reproducible generation
	Seed int `json:"seed,omitempty"`
	// ReturnURL indicates whether to return a URL instead of binary data
	ReturnURL bool `json:"-"`
}

// SiliconFlowRequestBody represents the request body for the SiliconFlow API
type SiliconFlowRequestBody struct {
	// Model is the AI model to use for image generation
	Model string `json:"model"`
	// Prompt is the text description of the image to generate
	Prompt string `json:"prompt"`
	// NegativePrompt specifies what should not appear in the generated image
	NegativePrompt string `json:"negative_prompt,omitempty"`
	// ImageSize is the dimensions of the image in format "widthxheight"
	ImageSize string `json:"image_size"`
	// BatchSize is the number of images to generate in one request
	BatchSize int `json:"batch_size"`
	// NumInferenceSteps controls the number of diffusion steps (higher = better quality but slower)
	NumInferenceSteps int `json:"num_inference_steps"`
	// GuidanceScale controls how closely the image follows the prompt (higher = more faithful)
	GuidanceScale float64 `json:"guidance_scale"`
}

// SiliconFlowImage represents an image in the SiliconFlow API response
type SiliconFlowImage struct {
	// URL is the location where the generated image can be downloaded
	URL string `json:"url"`
}

// SiliconFlowResponseBody represents the response body from the SiliconFlow API
type SiliconFlowResponseBody struct {
	// Images is an array of generated images
	Images []SiliconFlowImage `json:"images"`
}

// ImageResult represents the result of image generation
type ImageResult struct {
	// URL is the location where the generated image can be accessed
	URL string
	// Data contains the binary image data when downloaded
	Data []byte
}
