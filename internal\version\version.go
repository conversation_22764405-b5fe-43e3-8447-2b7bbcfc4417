package version

import (
	"os"
)

// Version is the current version of the application
// This will be overwritten by the build process
var Version = "0.0.0"

// Initialize sets up the version information
func Initialize() {
	// Check if version is set via environment variable
	if envVersion := os.Getenv("VERSION"); envVersion != "" {
		Version = envVersion
	}
}

// GetVersion returns the current version
func GetVersion() string {
	return Version
}
