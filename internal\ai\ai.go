package ai

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/gin-ai-backend/internal/ai/deepseek"
	"github.com/gin-ai-backend/internal/ai/gemini"
	"github.com/gin-ai-backend/internal/ai/grok"
	"github.com/gin-ai-backend/internal/ai/volcengine"
	"github.com/gin-ai-backend/internal/cache"
)

// Provider represents the AI service provider
type Provider string

const (
	OpenAI     Provider = "openai"
	DeepSeek   Provider = "deepseek"
	XAI        Provider = "xai"
	Grok       Provider = "grok"
	Gemini     Provider = "gemini"
	Volcengine Provider = "volcengine"
)

// ResponseGeneratorFunc is a function type for generating AI responses
type ResponseGeneratorFunc func(prompt string, provider ...Provider) (string, error)

// Client handles AI API interactions
type Client struct {
	OpenAIApiKey     string
	DeepSeekApiKey   string
	XAIApiKey        string
	GrokApiKey       string
	GeminiApiKey     string
	VolcengineApiKey string
	DefaultProvider  Provider
	GenerateResponse ResponseGeneratorFunc
}

// NewClient creates a new AI client
func NewClient(openAIKey, deepSeekKey, xaiKey, grokKey, geminiKey, volcengineKey string, defaultProvider string) *Client {
	provider := OpenAI
	if defaultProvider != "" {
		provider = Provider(defaultProvider)
	}

	client := &Client{
		OpenAIApiKey:     openAIKey,
		DeepSeekApiKey:   deepSeekKey,
		XAIApiKey:        xaiKey,
		GrokApiKey:       grokKey,
		GeminiApiKey:     geminiKey,
		VolcengineApiKey: volcengineKey,
		DefaultProvider:  provider,
	}

	// Set the GenerateResponse function
	client.GenerateResponse = client.generateResponse

	return client
}

// generateResponse generates a response from the AI model
func (c *Client) generateResponse(prompt string, provider ...Provider) (string, error) {
	selectedProvider := c.DefaultProvider
	if len(provider) > 0 {
		selectedProvider = provider[0]
	}

	// Generate cache key
	cacheKey := fmt.Sprintf("ai:%s:%s", selectedProvider, cache.MD5Hash(prompt))

	// Check cache
	if cachedResponse, found := cache.GlobalCache.Get(cacheKey); found {
		return cachedResponse.(string), nil
	}

	// Generate response based on provider
	var response string
	var err error

	switch selectedProvider {
	case OpenAI:
		response, err = c.generateOpenAI(prompt)
	case DeepSeek:
		response, err = c.generateDeepSeek(prompt)
	case XAI:
		response, err = c.generateXAI(prompt)
	case Grok:
		response, err = c.generateGrok(prompt)
	case Gemini:
		response, err = c.generateGemini(prompt)
	case Volcengine:
		response, err = c.generateVolcengine(prompt)
	default:
		return "", fmt.Errorf("unsupported AI provider: %s", selectedProvider)
	}

	if err != nil {
		return "", fmt.Errorf("error generating response via %s: %w", selectedProvider, err)
	}

	// Cache response for 1 hour
	cache.GlobalCache.Set(cacheKey, response, time.Hour)

	return response, nil
}

// OpenAI Implementation
type openAIRequestBody struct {
	Model    string          `json:"model"`
	Messages []openAIMessage `json:"messages"`
}

type openAIMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type openAIResponseBody struct {
	ID      string         `json:"id"`
	Object  string         `json:"object"`
	Created int            `json:"created"`
	Choices []openAIChoice `json:"choices"`
}

type openAIChoice struct {
	Index        int           `json:"index"`
	Message      openAIMessage `json:"message"`
	FinishReason string        `json:"finish_reason"`
}

func (c *Client) generateOpenAI(prompt string) (string, error) {
	if c.OpenAIApiKey == "" {
		return "OpenAI API key not configured", nil
	}

	reqBody := openAIRequestBody{
		Model: "gpt-3.5-turbo",
		Messages: []openAIMessage{
			{
				Role:    "user",
				Content: prompt,
			},
		},
	}

	reqJSON, err := json.Marshal(reqBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", "https://api.openai.com/v1/chat/completions", bytes.NewBuffer(reqJSON))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.OpenAIApiKey)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Handle non-200 response
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("OpenAI API returned non-200, got %s", resp.Status)
	}

	var respBody openAIResponseBody
	if err := json.NewDecoder(resp.Body).Decode(&respBody); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	if len(respBody.Choices) == 0 {
		return "", fmt.Errorf("no choices in response")
	}

	return respBody.Choices[0].Message.Content, nil
}

// DeepSeek Implementation
func (c *Client) generateDeepSeek(prompt string) (string, error) {
	if c.DeepSeekApiKey == "" {
		return "DeepSeek API key not configured", nil
	}

	// Set the API key in environment for the DeepSeek service
	os.Setenv("DEEPSEEK_API_KEY", c.DeepSeekApiKey)

	// Generate text using DeepSeek service
	response, err := deepseek.GenerateText(prompt, &deepseek.GenerateTextOptions{
		LogPrefix: "[DeepSeek]",
	})
	if err != nil {
		return "", fmt.Errorf("failed to generate content from DeepSeek: %w", err)
	}

	return response, nil
}

// XAI Implementation
type xaiRequestBody struct {
	Prompt    string `json:"prompt"`
	MaxTokens int    `json:"max_tokens"`
}

type xaiResponseBody struct {
	ID       string `json:"id"`
	Response string `json:"response"`
}

func (c *Client) generateXAI(prompt string) (string, error) {
	if c.XAIApiKey == "" {
		return "XAI API key not configured", nil
	}

	reqBody := xaiRequestBody{
		Prompt:    prompt,
		MaxTokens: 1000,
	}

	reqJSON, err := json.Marshal(reqBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", "https://api.xai.com/v1/completions", bytes.NewBuffer(reqJSON))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.XAIApiKey)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Handle non-200 response
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("XAI API returned non-200 status: %s", resp.Status)
	}

	var respBody xaiResponseBody
	if err := json.NewDecoder(resp.Body).Decode(&respBody); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return respBody.Response, nil
}

// Google Gemini Implementation
func (c *Client) generateGemini(prompt string) (string, error) {
	if c.GeminiApiKey == "" {
		return "Google Gemini API key not configured", nil
	}

	// Set the API key in environment for the Gemini service
	os.Setenv("GEMINI_API_KEY", c.GeminiApiKey)

	// Generate text using Gemini service
	response, err := gemini.GenerateText(prompt, &gemini.GenerateTextOptions{
		Temperature:     0.7,
		MaxOutputTokens: 2048,
		TopP:            0.95,
		TopK:            40,
		LogPrefix:       "[Gemini]",
	})
	if err != nil {
		return "", fmt.Errorf("failed to generate content from Gemini: %w", err)
	}

	return response, nil
}

// Grok Implementation
func (c *Client) generateGrok(prompt string) (string, error) {
	if c.GrokApiKey == "" {
		return "Grok API key not configured", nil
	}

	// Set the API key in environment for the Grok service
	os.Setenv("XAI_API_KEY", c.GrokApiKey)

	// Generate text using Grok service
	response, err := grok.GenerateText(prompt, &grok.GenerateTextOptions{
		SystemPrompt: "你是一个专业的AI助手，提供有用、安全、准确的信息。",
		Temperature:  0.7,
		MaxTokens:    2048,
		LogPrefix:    "[Grok]",
	})
	if err != nil {
		return "", fmt.Errorf("failed to generate content from Grok: %w", err)
	}

	return response, nil
}

// Volcengine Implementation
func (c *Client) generateVolcengine(prompt string) (string, error) {
	if c.VolcengineApiKey == "" {
		return "Volcengine API key not configured", nil
	}

	// Set the API key in environment for the Volcengine service
	os.Setenv("VOLCENGINE_API_KEY", c.VolcengineApiKey)

	// Define system prompt
	systemPrompt := "你是一个专业的AI助手，提供有用、安全、准确的信息。"

	// Generate text using Volcengine service
	response, err := volcengine.GenerateText(systemPrompt, prompt, &volcengine.GenerateTextOptions{
		Temperature: 0.7,
		MaxTokens:   2048,
		LogPrefix:   "[Volcengine]",
	})
	if err != nil {
		return "", fmt.Errorf("failed to generate content from Volcengine: %w", err)
	}

	return response, nil
}
