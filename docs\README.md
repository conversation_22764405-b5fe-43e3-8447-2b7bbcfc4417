# Swagger Documentation

This directory contains the Swagger/OpenAPI documentation for the Gin AI Backend API.

## Accessing the Documentation

When the server is running, you can access the Swagger UI at:

```
http://localhost:8080/swagger/index.html
```

## Updating the Documentation

The documentation is generated from annotations in the code. To update the documentation, you need to:

1. Update the annotations in the code
2. Run the `swag init` command to regenerate the documentation

```bash
swag init -g cmd/main.go
```

## Documentation Structure

- `swagger.go` - Contains the Swagger configuration
- `docs.go` - Contains the generated Swagger documentation
- `swagger.json` - Generated Swagger JSON file
- `swagger.yaml` - Generated Swagger YAML file
