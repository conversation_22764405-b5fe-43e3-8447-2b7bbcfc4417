package gemini

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/google/generative-ai-go/genai"
	"github.com/joho/godotenv"
	"google.golang.org/api/option"
)

// GeminiService is a client for the Google Gemini AI service
type GeminiService struct {
	APIKey       string
	DefaultModel string
}

// NewGeminiService creates a new Gemini service client
func NewGeminiService() *GeminiService {
	// Try to load .env file
	_ = godotenv.Load()

	apiKey := os.Getenv("GEMINI_API_KEY")
	defaultModel := os.Getenv("GEMINI_MODEL_ID")

	// Set default values
	if defaultModel == "" {
		defaultModel = "gemini-pro"
	}

	log.Println("Initializing GeminiService...")
	service := &GeminiService{
		APIKey:       apiKey,
		DefaultModel: defaultModel,
	}

	if apiKey == "" {
		log.Println("Warning: GEMINI_API_KEY not set in environment variables")
	} else {
		log.Println("GeminiService initialized with API key")
	}

	log.Printf("Using Gemini default model: %s\n", defaultModel)

	return service
}

// GenerateText generates text using the Gemini API
//
// Parameters:
//   - prompt: The user query or input text
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - The generated text response
//   - Any error that occurred during the process
func (s *GeminiService) GenerateText(prompt string, options *GenerateTextOptions) (string, error) {
	if options == nil {
		options = &GenerateTextOptions{}
	}

	// Set default values
	model := options.Model
	if model == "" {
		model = s.DefaultModel
	}

	temperature := options.Temperature
	if temperature == 0 {
		temperature = 0.7
	}

	maxOutputTokens := options.MaxOutputTokens
	if maxOutputTokens == 0 {
		maxOutputTokens = 2048
	}

	topP := options.TopP
	if topP == 0 {
		topP = 0.95
	}

	topK := options.TopK
	if topK == 0 {
		topK = 40
	}

	logPrefix := options.LogPrefix
	if logPrefix == "" {
		logPrefix = "[Gemini]"
	}

	if s.APIKey == "" {
		log.Printf("%s GEMINI_API_KEY not set, cannot generate content\n", logPrefix)
		return "", fmt.Errorf("GEMINI_API_KEY not set")
	}

	log.Printf("%s Calling Gemini API...\n", logPrefix)
	log.Printf("%s Prompt length: %d\n", logPrefix, len(prompt))

	// Create context
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Initialize Gemini client
	client, err := genai.NewClient(ctx, option.WithAPIKey(s.APIKey))
	if err != nil {
		return "", fmt.Errorf("failed to create Gemini client: %w", err)
	}
	defer client.Close()

	// Get Gemini model
	geminiModel := client.GenerativeModel(model)

	// Configure model
	geminiModel.SetTemperature(temperature)
	geminiModel.SetTopP(topP)
	geminiModel.SetTopK(topK)
	geminiModel.SetMaxOutputTokens(maxOutputTokens)

	// Create prompt
	promptParts := []genai.Part{
		genai.Text(prompt),
	}

	startTime := time.Now()
	log.Printf("%s Sending request to Gemini AI...\n", logPrefix)

	// Generate content
	resp, err := geminiModel.GenerateContent(ctx, promptParts...)
	if err != nil {
		return "", fmt.Errorf("failed to generate content: %w", err)
	}

	elapsedTime := time.Since(startTime).Milliseconds()
	log.Printf("%s Gemini API response time: %dms\n", logPrefix, elapsedTime)

	// Check for valid response
	if resp == nil || len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("Gemini did not return a valid response")
	}

	// Extract response text
	var responseText string
	for _, part := range resp.Candidates[0].Content.Parts {
		if textPart, ok := part.(genai.Text); ok {
			responseText += string(textPart)
		}
	}

	log.Printf("%s Received Gemini AI response, length: %d\n", logPrefix, len(responseText))
	return responseText, nil
}

// StreamText generates text using the Gemini API in streaming mode
//
// Parameters:
//   - prompt: The user query or input text
//   - callback: Function to call for each chunk of generated text
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - Any error that occurred during the process
func (s *GeminiService) StreamText(prompt string, callback StreamTextCallback, options *GenerateTextOptions) error {
	if options == nil {
		options = &GenerateTextOptions{}
	}

	// Set default values
	model := options.Model
	if model == "" {
		model = s.DefaultModel
	}

	temperature := options.Temperature
	if temperature == 0 {
		temperature = 0.7
	}

	maxOutputTokens := options.MaxOutputTokens
	if maxOutputTokens == 0 {
		maxOutputTokens = 2048
	}

	topP := options.TopP
	if topP == 0 {
		topP = 0.95
	}

	topK := options.TopK
	if topK == 0 {
		topK = 40
	}

	logPrefix := options.LogPrefix
	if logPrefix == "" {
		logPrefix = "[Gemini]"
	}

	if s.APIKey == "" {
		log.Printf("%s GEMINI_API_KEY not set, cannot generate content\n", logPrefix)
		return fmt.Errorf("GEMINI_API_KEY not set")
	}

	log.Printf("%s Calling Gemini API for streaming generation...\n", logPrefix)
	log.Printf("%s Prompt length: %d\n", logPrefix, len(prompt))

	// Create context
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	// Initialize Gemini client
	client, err := genai.NewClient(ctx, option.WithAPIKey(s.APIKey))
	if err != nil {
		return fmt.Errorf("failed to create Gemini client: %w", err)
	}
	defer client.Close()

	// Get Gemini model
	geminiModel := client.GenerativeModel(model)

	// Configure model
	geminiModel.SetTemperature(temperature)
	geminiModel.SetTopP(topP)
	geminiModel.SetTopK(topK)
	geminiModel.SetMaxOutputTokens(maxOutputTokens)

	// Create prompt
	promptParts := []genai.Part{
		genai.Text(prompt),
	}

	startTime := time.Now()
	log.Printf("%s Sending streaming request to Gemini AI...\n", logPrefix)

	// Stream content generation
	iter := geminiModel.GenerateContentStream(ctx, promptParts...)

	var fullText string
	for {
		resp, err := iter.Next()
		if err != nil {
			if err.Error() == "EOF" {
				// Stream ended
				break
			}
			return fmt.Errorf("failed to stream content: %w", err)
		}

		// Extract response text
		var chunkText string
		for _, part := range resp.Candidates[0].Content.Parts {
			if textPart, ok := part.(genai.Text); ok {
				chunkText += string(textPart)
			}
		}

		fullText += chunkText
		callback(chunkText, false)
	}

	elapsedTime := time.Since(startTime).Milliseconds()
	log.Printf("%s Gemini API streaming response complete, time: %dms, total length: %d\n", logPrefix, elapsedTime, len(fullText))
	callback("", true)

	return nil
}

// Chat conducts a conversation using the Gemini API
//
// Parameters:
//   - message: The current user message to send
//   - options: Configuration options including conversation history (can be nil for defaults)
//
// Returns:
//   - The generated response from the assistant
//   - Any error that occurred during the process
func (s *GeminiService) Chat(message string, options *ChatOptions) (string, error) {
	if options == nil {
		options = &ChatOptions{}
	}

	// Set default values
	model := options.Model
	if model == "" {
		model = s.DefaultModel
	}

	temperature := options.Temperature
	if temperature == 0 {
		temperature = 0.7
	}

	maxOutputTokens := options.MaxOutputTokens
	if maxOutputTokens == 0 {
		maxOutputTokens = 2048
	}

	topP := options.TopP
	if topP == 0 {
		topP = 0.95
	}

	topK := options.TopK
	if topK == 0 {
		topK = 40
	}

	if s.APIKey == "" {
		log.Println("[Gemini] GEMINI_API_KEY not set, cannot generate content")
		return "", fmt.Errorf("GEMINI_API_KEY not set")
	}

	log.Println("[Gemini] Calling Gemini API for chat...")
	log.Printf("[Gemini] Message length: %d\n", len(message))

	// Create context
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Initialize Gemini client
	client, err := genai.NewClient(ctx, option.WithAPIKey(s.APIKey))
	if err != nil {
		return "", fmt.Errorf("failed to create Gemini client: %w", err)
	}
	defer client.Close()

	// Get Gemini model
	geminiModel := client.GenerativeModel(model)

	// Configure model
	geminiModel.SetTemperature(temperature)
	geminiModel.SetTopP(topP)
	geminiModel.SetTopK(topK)
	geminiModel.SetMaxOutputTokens(maxOutputTokens)

	// Create chat session
	chat := geminiModel.StartChat()

	// Add system prompt
	if options.SystemPrompt != "" {
		chat.History = append(chat.History, &genai.Content{
			Parts: []genai.Part{genai.Text(options.SystemPrompt)},
			Role:  "user",
		})
		chat.History = append(chat.History, &genai.Content{
			Parts: []genai.Part{genai.Text("I understand and will act according to your instructions.")},
			Role:  "model",
		})
	}

	// Add chat history
	for _, msg := range options.History {
		role := "user"
		if msg.Role == "model" || msg.Role == "assistant" {
			role = "model"
		}

		chat.History = append(chat.History, &genai.Content{
			Parts: []genai.Part{genai.Text(msg.Content)},
			Role:  role,
		})
	}

	startTime := time.Now()
	log.Println("[Gemini] Sending chat request to Gemini AI...")

	// Send message
	resp, err := chat.SendMessage(ctx, genai.Text(message))
	if err != nil {
		return "", fmt.Errorf("failed to send chat message: %w", err)
	}

	elapsedTime := time.Since(startTime).Milliseconds()
	log.Printf("[Gemini] Gemini API chat response time: %dms\n", elapsedTime)

	// Extract response text
	var responseText string
	for _, part := range resp.Candidates[0].Content.Parts {
		if textPart, ok := part.(genai.Text); ok {
			responseText += string(textPart)
		}
	}

	log.Printf("[Gemini] Received Gemini AI chat response, length: %d\n", len(responseText))
	return responseText, nil
}

// Create singleton instance
var geminiService *GeminiService

// GetGeminiService returns the singleton instance of the Gemini service
func GetGeminiService() *GeminiService {
	if geminiService == nil {
		geminiService = NewGeminiService()
	}
	return geminiService
}
