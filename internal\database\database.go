package database

import (
	"fmt"
	"log"

	"github.com/gin-ai-backend/internal/models"
	puresqlite "github.com/glebarez/sqlite" // Pure Go SQLite driver
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// DB is the database instance
var DB *gorm.DB

// Initialize connects to the database and performs migrations
func Initialize(dbType, dbURL, dbPath string) error {
	var err error

	switch dbType {
	case "sqlite":
		DB, err = initSQLite(dbPath)
	case "supabase", "neon":
		DB, err = initPostgres(dbURL)
	default:
		return fmt.Errorf("unsupported database type: %s", dbType)
	}

	if err != nil {
		log.Printf("Failed to connect to database: %v", err)
		return fmt.Errorf("failed to initialize database: %w", err)
	}

	// Auto migrate the schema
	err = DB.AutoMigrate(&models.User{})
	if err != nil {
		log.Printf("Failed to migrate database: %v", err)
		return fmt.Errorf("failed to migrate database: %w", err)
	}

	log.Printf("Database connection established (%s)", dbType)
	return nil
}

// initSQLite initializes a SQLite database connection
func initSQLite(dbPath string) (*gorm.DB, error) {
	// Try to use the CGO-based SQLite driver first
	// If it fails, fall back to the pure Go SQLite driver
	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{})
	if err != nil {
		log.Println("CGO-based SQLite driver failed, falling back to pure Go SQLite driver:", err)
		db, err = gorm.Open(puresqlite.Open(dbPath), &gorm.Config{})
		if err != nil {
			return nil, fmt.Errorf("failed to open SQLite database: %w", err)
		}
		return db, nil
	}

	log.Println("Using CGO-based SQLite driver")
	return db, nil
}

// initPostgres initializes a PostgreSQL database connection (works for both Supabase and Neon)
func initPostgres(dbURL string) (*gorm.DB, error) {
	if dbURL == "" {
		return nil, fmt.Errorf("database URL is required for PostgreSQL connections")
	}
	db, err := gorm.Open(postgres.Open(dbURL), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to open Postgres database: %w", err)
	}
	return db, nil
}

// GetDB returns the database instance
func GetDB() *gorm.DB {
	return DB
}
