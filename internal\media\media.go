package media

import (
	"bytes"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// MediaProcessor handles media processing operations
type MediaProcessor struct {
	TempDir string
}

// NewMediaProcessor creates a new media processor
func NewMediaProcessor(tempDir string) (*MediaProcessor, error) {
	// Create temp directory if it doesn't exist
	if err := os.MkdirAll(tempDir, 0o755); err != nil {
		return nil, fmt.Errorf("failed to create temp directory: %w", err)
	}

	return &MediaProcessor{
		TempDir: tempDir,
	}, nil
}

// ImageFormat represents supported image formats
type ImageFormat string

const (
	JPEG ImageFormat = "jpeg"
	PNG  ImageFormat = "png"
	GIF  ImageFormat = "gif"
	WEBP ImageFormat = "webp"
	AVIF ImageFormat = "avif"
)

// AudioFormat represents supported audio formats
type AudioFormat string

const (
	MP3  AudioFormat = "mp3"
	WAV  AudioFormat = "wav"
	OGG  AudioFormat = "ogg"
	FLAC AudioFormat = "flac"
	AAC  AudioFormat = "aac"
)

// ResizeOptions contains options for image resizing
type ResizeOptions struct {
	Width  int
	Height int
	// Maintain aspect ratio if true
	Preserve bool
}

// ConvertImageFormat converts an image from one format to another
func (m *MediaProcessor) ConvertImageFormat(imageData []byte, fromFormat, toFormat ImageFormat) ([]byte, error) {
	// Create temporary input file
	inputPath := filepath.Join(m.TempDir, fmt.Sprintf("input.%s", fromFormat))
	if err := os.WriteFile(inputPath, imageData, 0o644); err != nil {
		return nil, fmt.Errorf("failed to write input file: %w", err)
	}
	defer os.Remove(inputPath)

	// Create output path
	outputPath := filepath.Join(m.TempDir, fmt.Sprintf("output.%s", toFormat))
	defer os.Remove(outputPath)

	// Use ImageMagick to convert the image
	cmd := exec.Command("convert", inputPath, outputPath)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("failed to convert image: %s, error: %w", stderr.String(), err)
	}

	// Read the converted image
	outputData, err := os.ReadFile(outputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read converted image: %w", err)
	}

	return outputData, nil
}

// ResizeImage resizes an image according to the provided options
func (m *MediaProcessor) ResizeImage(imageData []byte, format ImageFormat, options ResizeOptions) ([]byte, error) {
	// Create temporary input file
	inputPath := filepath.Join(m.TempDir, fmt.Sprintf("input.%s", format))
	if err := os.WriteFile(inputPath, imageData, 0o644); err != nil {
		return nil, fmt.Errorf("failed to write input file: %w", err)
	}
	defer os.Remove(inputPath)

	// Create output path
	outputPath := filepath.Join(m.TempDir, fmt.Sprintf("output.%s", format))
	defer os.Remove(outputPath)

	// Prepare resize dimensions
	dimensions := fmt.Sprintf("%dx%d", options.Width, options.Height)
	if options.Preserve {
		dimensions += ">"
	}

	// Use ImageMagick to resize the image
	cmd := exec.Command("convert", inputPath, "-resize", dimensions, outputPath)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("failed to resize image: %s, error: %w", stderr.String(), err)
	}

	// Read the resized image
	outputData, err := os.ReadFile(outputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read resized image: %w", err)
	}

	return outputData, nil
}

// ConvertAudioFormat converts audio from one format to another
func (m *MediaProcessor) ConvertAudioFormat(audioData []byte, fromFormat, toFormat AudioFormat) ([]byte, error) {
	// Create temporary input file
	inputPath := filepath.Join(m.TempDir, fmt.Sprintf("input.%s", fromFormat))
	if err := os.WriteFile(inputPath, audioData, 0o644); err != nil {
		return nil, fmt.Errorf("failed to write input file: %w", err)
	}
	defer os.Remove(inputPath)

	// Create output path
	outputPath := filepath.Join(m.TempDir, fmt.Sprintf("output.%s", toFormat))
	defer os.Remove(outputPath)

	// Use FFmpeg to convert the audio
	cmd := exec.Command("ffmpeg", "-i", inputPath, "-y", outputPath)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("failed to convert audio: %s, error: %w", stderr.String(), err)
	}

	// Read the converted audio
	outputData, err := os.ReadFile(outputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read converted audio: %w", err)
	}

	return outputData, nil
}

// GetImageFormat determines the image format from a filename
func GetImageFormat(filename string) ImageFormat {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".jpg", ".jpeg":
		return JPEG
	case ".png":
		return PNG
	case ".gif":
		return GIF
	case ".webp":
		return WEBP
	case ".avif":
		return AVIF
	default:
		return PNG // Default to PNG
	}
}

// GetAudioFormat determines the audio format from a filename
func GetAudioFormat(filename string) AudioFormat {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".mp3":
		return MP3
	case ".wav":
		return WAV
	case ".ogg":
		return OGG
	case ".flac":
		return FLAC
	case ".aac":
		return AAC
	default:
		return MP3 // Default to MP3
	}
}
