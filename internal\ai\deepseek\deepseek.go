package deepseek

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/joho/godotenv"
)

// DeepSeekService is a client for the DeepSeek AI service
type DeepSeekService struct {
	// APIKey is the authentication key for the DeepSeek API
	APIKey string
	// APIURL is the endpoint URL for the DeepSeek API
	APIURL string
	// ModelID is the identifier for the AI model to use
	ModelID string
}

// NewDeepSeekService creates a new DeepSeek service client
// It loads configuration from environment variables and sets default values if needed
func NewDeepSeekService() *DeepSeekService {
	// Try to load .env file
	_ = godotenv.Load()

	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	apiURL := os.Getenv("DEEPSEEK_API_URL")
	modelID := os.Getenv("DEEPSEEK_MODEL_ID")

	// Set default values
	if apiURL == "" {
		apiURL = "https://api.deepseek.com/v1/chat/completions"
	}
	if modelID == "" {
		modelID = "deepseek-chat"
	}

	log.Println("Initializing DeepSeekService...")
	service := &DeepSeekService{
		APIKey:  apiKey,
		APIURL:  apiURL,
		ModelID: modelID,
	}

	if apiKey == "" {
		log.Println("Warning: DEEPSEEK_API_KEY not set in environment variables")
	} else {
		log.Println("DeepSeekService initialized with API key")
	}

	log.Printf("Using DeepSeek API URL: %s\n", apiURL)
	log.Printf("Using DeepSeek model ID: %s\n", modelID)

	return service
}

// GenerateText generates text using the DeepSeek API
//
// Parameters:
//   - prompt: The user query or input text
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - The generated text response
//   - Any error that occurred during the process
func (s *DeepSeekService) GenerateText(prompt string, options *GenerateTextOptions) (string, error) {
	if options == nil {
		options = &GenerateTextOptions{}
	}

	// Set default values
	model := options.Model
	if model == "" {
		model = s.ModelID
	}

	if s.APIKey == "" {
		log.Printf("%s DEEPSEEK_API_KEY not set, cannot generate content\n", options.LogPrefix)
		return "", fmt.Errorf("DEEPSEEK_API_KEY not set")
	}

	log.Printf("%s Calling DeepSeek API...\n", options.LogPrefix)
	log.Printf("%s Prompt length: %d\n", options.LogPrefix, len(prompt))

	// Build request data
	requestData := DeepSeekRequestBody{
		Model: model,
		Messages: []DeepSeekMessage{
			{Role: "user", Content: prompt},
		},
	}

	// Serialize request data
	requestJSON, err := json.Marshal(requestData)
	if err != nil {
		return "", fmt.Errorf("Failed to serialize request data: %w", err)
	}

	startTime := time.Now()
	log.Printf("%s Sending request to DeepSeek AI...\n", options.LogPrefix)

	// Create HTTP request
	req, err := http.NewRequest("POST", s.APIURL, bytes.NewBuffer(requestJSON))
	if err != nil {
		return "", fmt.Errorf("Failed to create HTTP request: %w", err)
	}

	// Add request headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.APIKey)

	// Send request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("Failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	elapsedTime := time.Since(startTime).Milliseconds()
	log.Printf("%s DeepSeek API response time: %dms, status code: %d\n", options.LogPrefix, elapsedTime, resp.StatusCode)

	// Check response status code
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API returned non-200 status code: %d", resp.StatusCode)
	}

	// Parse response
	var responseBody DeepSeekResponseBody
	if err := json.NewDecoder(resp.Body).Decode(&responseBody); err != nil {
		return "", fmt.Errorf("Failed to parse response: %w", err)
	}

	// Extract response content
	if len(responseBody.Choices) > 0 {
		content := responseBody.Choices[0].Message.Content
		log.Printf("%s Received DeepSeek AI response, length: %d\n", options.LogPrefix, len(content))
		return content, nil
	}

	log.Printf("%s Unexpected API response structure\n", options.LogPrefix)
	return "", fmt.Errorf("No content in API response")
}

// deepSeekService is the singleton instance of DeepSeekService
var deepSeekService *DeepSeekService

// GetDeepSeekService returns the singleton instance of the DeepSeek service
func GetDeepSeekService() *DeepSeekService {
	if deepSeekService == nil {
		deepSeekService = NewDeepSeekService()
	}
	return deepSeekService
}
