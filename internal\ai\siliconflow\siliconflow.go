package siliconflow

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/joho/godotenv"
)

// SiliconFlowService is a client for the SiliconFlow image generation service
type SiliconFlowService struct {
	// APIKey is the authentication key for the SiliconFlow API
	APIKey string
}

// NewSiliconFlowService creates a new SiliconFlow service client
// It loads configuration from environment variables
func NewSiliconFlowService() *SiliconFlowService {
	// Try to load .env file
	_ = godotenv.Load()

	apiKey := os.Getenv("SILICONFLOW_API_KEY")

	log.Println("Initializing SiliconFlowService...")
	service := &SiliconFlowService{
		APIKey: apiKey,
	}

	if apiKey == "" {
		log.Println("Warning: SILICONFLOW_API_KEY not set in environment variables")
	} else {
		log.Println("SiliconFlowService initialized with API key")
	}

	return service
}

// GenerateImageWithSiliconFlow generates an image using SiliconFlow
//
// Parameters:
//   - prompt: The text description of the image to generate
//   - config: Configuration options for the image generation (can be nil for defaults)
//
// Returns:
//   - The generated image result (URL or binary data)
//   - Any error that occurred during the process
func (s *SiliconFlowService) GenerateImageWithSiliconFlow(prompt string, config *ImageGenerationConfig) (*ImageResult, error) {
	log.Printf("[SiliconFlow] Starting image generation, prompt: %s...\n", truncateString(prompt, 30))

	if s.APIKey == "" {
		return nil, fmt.Errorf("SILICONFLOW_API_KEY not set, cannot generate image")
	}

	// Set default values
	width := 1024
	if config != nil && config.Width > 0 {
		width = config.Width
	}

	height := 1024
	if config != nil && config.Height > 0 {
		height = config.Height
	}

	returnURL := true
	if config != nil {
		returnURL = config.ReturnURL
	}

	// Default negative prompt
	negativePrompt := "text, watermarks, logos, signatures, distortion, low quality, blurry, out of focus, unaesthetic, grainy, low-resolution, oversaturated, harsh lighting, jarring colors, people, faces, scary, disturbing"
	if config != nil && config.NegativePrompt != "" {
		negativePrompt = config.NegativePrompt
	}

	// Prepare request data
	requestData := SiliconFlowRequestBody{
		Model:             "Kwai-Kolors/Kolors",
		Prompt:            prompt,
		NegativePrompt:    negativePrompt,
		ImageSize:         fmt.Sprintf("%dx%d", width, height),
		BatchSize:         1,
		NumInferenceSteps: 30,  // Increase steps for better quality
		GuidanceScale:     7.0, // Balance between accuracy and creativity
	}

	// Serialize request data
	requestJSON, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("Failed to serialize request data: %w", err)
	}

	// Set request headers
	headers := map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", s.APIKey),
		"Content-Type":  "application/json",
	}

	log.Println("[SiliconFlow] Sending request to image generation API...")
	startTime := time.Now()

	// Create HTTP request
	req, err := http.NewRequest("POST", "https://api.siliconflow.cn/v1/images/generations", bytes.NewBuffer(requestJSON))
	if err != nil {
		return nil, fmt.Errorf("Failed to create HTTP request: %w", err)
	}

	// Add request headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// Send request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("Failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	elapsedTime := time.Since(startTime).Milliseconds()
	log.Printf("[SiliconFlow] API response time: %dms, status code: %d\n", elapsedTime, resp.StatusCode)

	// Check response status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("[SiliconFlow] Image generation failed, status code: %d", resp.StatusCode)
	}

	// Parse response
	var responseBody SiliconFlowResponseBody
	if err := json.NewDecoder(resp.Body).Decode(&responseBody); err != nil {
		return nil, fmt.Errorf("Failed to parse response: %w", err)
	}

	// Check if there are any images
	if len(responseBody.Images) == 0 {
		return nil, fmt.Errorf("[SiliconFlow] API did not return any images")
	}

	// Get image URL
	imageURL := responseBody.Images[0].URL
	log.Printf("[SiliconFlow] Received image URL: %s...\n", truncateString(imageURL, 50))

	// If only URL is needed, return directly
	if returnURL {
		return &ImageResult{URL: imageURL}, nil
	}

	// Otherwise download image data
	log.Println("[SiliconFlow] Downloading image data...")
	imageResp, err := http.Get(imageURL)
	if err != nil {
		return nil, fmt.Errorf("[SiliconFlow] Failed to download image, URL: %s: %w", imageURL, err)
	}
	defer imageResp.Body.Close()

	// Check response status code
	if imageResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("[SiliconFlow] Failed to download image, status code: %d", imageResp.StatusCode)
	}

	// Read image data
	imageData, err := io.ReadAll(imageResp.Body)
	if err != nil {
		return nil, fmt.Errorf("[SiliconFlow] Failed to read image data: %w", err)
	}

	log.Printf("[SiliconFlow] Image content downloaded, size: %.2fKB\n", float64(len(imageData))/1024)

	// Return image data
	return &ImageResult{Data: imageData}, nil
}

// SaveImage saves an image to a file
//
// Parameters:
//   - imageData: The binary image data to save
//   - outputPath: The file path where the image should be saved
//
// Returns:
//   - The path where the image was saved
//   - Any error that occurred during the process
func (s *SiliconFlowService) SaveImage(imageData []byte, outputPath string) (string, error) {
	// Ensure output directory exists
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0o755); err != nil {
		return "", fmt.Errorf("Failed to create output directory: %w", err)
	}

	// Save image
	if err := os.WriteFile(outputPath, imageData, 0o644); err != nil {
		return "", fmt.Errorf("Failed to save image: %w", err)
	}

	log.Printf("[SiliconFlow] Image saved to %s\n", outputPath)
	return outputPath, nil
}

// truncateString is a helper function to truncate a string to a maximum length
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// siliconFlowService is the singleton instance of SiliconFlowService
var siliconFlowService *SiliconFlowService

// GetSiliconFlowService returns the singleton instance of the SiliconFlow service
func GetSiliconFlowService() *SiliconFlowService {
	if siliconFlowService == nil {
		siliconFlowService = NewSiliconFlowService()
	}
	return siliconFlowService
}
