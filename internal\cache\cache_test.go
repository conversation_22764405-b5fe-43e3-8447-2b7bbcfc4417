package cache

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNewCache(t *testing.T) {
	cache := NewCache()
	assert.NotNil(t, cache)
	assert.NotNil(t, cache.items)
}

func TestCacheSetAndGet(t *testing.T) {
	cache := NewCache()

	// Test setting and getting a value
	cache.Set("key1", "value1", time.Hour)
	value, found := cache.Get("key1")
	assert.True(t, found)
	assert.Equal(t, "value1", value)

	// Test getting a non-existent key
	value, found = cache.Get("nonexistent")
	assert.False(t, found)
	assert.Nil(t, value)
}

func TestCacheExpiration(t *testing.T) {
	cache := NewCache()

	// Set a value with short expiration
	cache.Set("expiring", "value", 10*time.Millisecond)
	
	// Should be available immediately
	value, found := cache.Get("expiring")
	assert.True(t, found)
	assert.Equal(t, "value", value)

	// Wait for expiration
	time.Sleep(20 * time.Millisecond)

	// Should be expired now
	value, found = cache.Get("expiring")
	assert.False(t, found)
	assert.Nil(t, value)
}

func TestCacheNoExpiration(t *testing.T) {
	cache := NewCache()

	// Set a value with no expiration (duration = 0)
	cache.Set("permanent", "value", 0)
	
	value, found := cache.Get("permanent")
	assert.True(t, found)
	assert.Equal(t, "value", value)

	// Should still be available after some time
	time.Sleep(10 * time.Millisecond)
	value, found = cache.Get("permanent")
	assert.True(t, found)
	assert.Equal(t, "value", value)
}

func TestCacheDelete(t *testing.T) {
	cache := NewCache()

	// Set a value
	cache.Set("deleteme", "value", time.Hour)
	value, found := cache.Get("deleteme")
	assert.True(t, found)
	assert.Equal(t, "value", value)

	// Delete the value
	cache.Delete("deleteme")
	value, found = cache.Get("deleteme")
	assert.False(t, found)
	assert.Nil(t, value)
}

func TestCacheClear(t *testing.T) {
	cache := NewCache()

	// Set multiple values
	cache.Set("key1", "value1", time.Hour)
	cache.Set("key2", "value2", time.Hour)
	cache.Set("key3", "value3", time.Hour)

	// Verify they exist
	_, found1 := cache.Get("key1")
	_, found2 := cache.Get("key2")
	_, found3 := cache.Get("key3")
	assert.True(t, found1)
	assert.True(t, found2)
	assert.True(t, found3)

	// Clear the cache
	cache.Clear()

	// Verify they're gone
	_, found1 = cache.Get("key1")
	_, found2 = cache.Get("key2")
	_, found3 = cache.Get("key3")
	assert.False(t, found1)
	assert.False(t, found2)
	assert.False(t, found3)
}

func TestItemExpired(t *testing.T) {
	// Test non-expiring item
	item := Item{
		Value:      "test",
		Expiration: 0,
	}
	assert.False(t, item.Expired())

	// Test expired item
	item = Item{
		Value:      "test",
		Expiration: time.Now().Add(-time.Hour).UnixNano(),
	}
	assert.True(t, item.Expired())

	// Test non-expired item
	item = Item{
		Value:      "test",
		Expiration: time.Now().Add(time.Hour).UnixNano(),
	}
	assert.False(t, item.Expired())
}

func TestMD5Hash(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"hello", "5d41402abc4b2a76b9719d911017c592"},
		{"world", "7d793037a0760186574b0282f2f435e7"},
		{"", "d41d8cd98f00b204e9800998ecf8427e"},
		{"The quick brown fox", "a2004f37730b9445670a738fa0fc9ee5"},
	}

	for _, test := range tests {
		result := MD5Hash(test.input)
		assert.Equal(t, test.expected, result, "MD5 hash mismatch for input: %s", test.input)
	}
}

func TestCacheConcurrency(t *testing.T) {
	cache := NewCache()

	// Test concurrent access
	done := make(chan bool, 10)

	// Start multiple goroutines that set and get values
	for i := 0; i < 10; i++ {
		go func(id int) {
			key := fmt.Sprintf("key%d", id)
			value := fmt.Sprintf("value%d", id)
			
			cache.Set(key, value, time.Hour)
			retrieved, found := cache.Get(key)
			
			assert.True(t, found)
			assert.Equal(t, value, retrieved)
			
			done <- true
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}
}

func TestGlobalCache(t *testing.T) {
	// Test that GlobalCache is initialized
	assert.NotNil(t, GlobalCache)

	// Test basic functionality
	GlobalCache.Set("global_test", "global_value", time.Hour)
	value, found := GlobalCache.Get("global_test")
	assert.True(t, found)
	assert.Equal(t, "global_value", value)

	// Clean up
	GlobalCache.Delete("global_test")
}
