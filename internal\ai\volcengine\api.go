package volcengine

// GenerateText generates text using the Volcengine AI API
//
// This is the main public API function for text generation with Volcengine.
// It uses the singleton instance of VolcengineService to handle the request.
//
// Parameters:
//   - systemPrompt: Instructions for the AI model about how to behave
//   - userPrompt: The actual user query or input text
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - The generated text response
//   - Any error that occurred during the process
func GenerateText(systemPrompt, userPrompt string, options *GenerateTextOptions) (string, error) {
	return GetVolcengineService().GenerateText(systemPrompt, userPrompt, options)
}
