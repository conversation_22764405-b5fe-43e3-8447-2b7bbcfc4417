# Docker 部署与调试指南

## 1. Dockerfile 编写详解

### 1.1 基础结构示例
```dockerfile
# 使用官方 Go 基础镜像
FROM golang:1.21-alpine

# 设置工作目录
WORKDIR /app

# 设置Go环境变量
ENV GO111MODULE=on \
    GOPROXY=https://goproxy.cn,direct

# 复制go.mod和go.sum文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 编译应用
RUN go build -o main .

# 声明服务端口（根据gin配置）
EXPOSE 8080

# 启动命令
CMD ["./main"]
```

### 1.2 关键指令解析
- `FROM golang:1.21-alpine`：使用轻量级alpine版本的Go镜像
- `ENV`：设置Go模块代理，加速依赖下载
- `COPY go.mod go.sum`：分阶段复制依赖文件（优化构建缓存）
- `RUN go mod download`：预先下载依赖
- `go build -o main`：编译生成可执行文件

### 1.3 多阶段构建（推荐）
```dockerfile
# 构建阶段
FROM golang:1.21-alpine AS builder

WORKDIR /app

ENV GO111MODULE=on \
    GOPROXY=https://goproxy.cn,direct

COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o main .

# 运行阶段
FROM alpine:latest

WORKDIR /app
COPY --from=builder /app/main .
COPY config ./config

EXPOSE 8080
CMD ["./main"]
```

## 2. macOS 调试指南

### 2.1 常用命令
```bash
# 构建镜像
docker build -t gin-ai-backend .

# 运行容器（开发模式）
docker run -d \
  -p 8080:8080 \
  -v $(pwd)/config:/app/config \
  --name gin-ai-container \
  gin-ai-backend

# 查看运行日志
docker logs -f gin-ai-container

# 进入容器调试
docker exec -it gin-ai-container sh
```

### 2.2 开发环境配置
1. **热重载设置**
   ```yaml
   # docker-compose.yml
   version: '3'
   services:
     app:
       build: .
       ports:
         - "8080:8080"
       volumes:
         - .:/app
         - go-cache:/go
       environment:
         - GIN_MODE=debug
   volumes:
     go-cache:
   ```

2. **使用Air实现热重载**
   ```dockerfile
   # Dockerfile.dev
   FROM golang:1.21-alpine

   RUN go install github.com/cosmtrek/air@latest

   WORKDIR /app
   COPY . .

   CMD ["air"]
   ```

### 2.3 调试技巧
1. **Delve调试器设置**
   - 安装delve：`go install github.com/go-delve/delve/cmd/dlv@latest`
   - 在容器中暴露调试端口：`-p 2345:2345`
   - 启动调试：`dlv debug --headless --listen=:2345 --api-version=2 --accept-multiclient`

2. **VS Code 配置**
   ```json
   {
     "version": "0.2.0",
     "configurations": [
       {
         "name": "Docker Debug",
         "type": "go",
         "request": "attach",
         "mode": "remote",
         "remotePath": "/app",
         "port": 2345,
         "host": "127.0.0.1"
       }
     ]
   }
   ```

### 2.4 常见问题排查
1. **权限问题**：
   - 使用`chmod +x main`确保可执行权限
   - 容器内使用非root用户运行

2. **网络问题**：
   - 使用`--network=host`测试网络连接
   - 检查防火墙设置

3. **性能优化**：
   - 使用`.dockerignore`排除不必要文件
   - 合理设置Go编译参数：`go build -ldflags="-s -w"`
   - 使用多阶段构建减小镜像体积

## 3. 生产环境部署建议
1. 使用多阶段构建最小化镜像
2. 配置健康检查
3. 设置资源限制
4. 使用Docker Compose管理服务
5. 实现优雅关闭

## 4.问题清单

### Docker基础概念
1. 在多阶段构建中，为什么我们使用两个FROM指令？第二阶段使用alpine:latest而不是golang:1.21-alpine的原因是什么？

### 构建优化
2. 为什么要先COPY go.mod和go.sum文件，然后执行go mod download，而不是直接COPY整个项目目录？这样做有什么优势？

### 环境配置
3. 在开发环境中，为什么要设置 GO111MODULE=on 和 GOPROXY 环境变量？这些配置的作用是什么？

### 卷挂载
4. 在docker-compose.yml中，为什么要创建并使用 go-cache 卷？这个卷的作用是什么？

### 调试技巧
5. 使用Delve调试器时，为什么要设置 --headless 和 --accept-multiclient 参数？这些参数的作用是什么？

### 性能优化
6. 使用 go build -ldflags="-s -w" 编译参数的目的是什么？这会对生成的二进制文件产生什么影响？

### 开发工具
7. Air工具的作用是什么？为什么在开发环境中需要使用它？

### 网络配置
8. 在使用 --network=host 参数时，这会对容器的网络环境产生什么影响？在什么情况下需要使用这个参数？

### 安全性
9. 在生产环境中，为什么建议在容器内使用非root用户运行应用？这样做有什么好处？

### 部署策略
10. 在生产环境部署建议中提到的"优雅关闭"是什么意思？为什么这个特性很重要？