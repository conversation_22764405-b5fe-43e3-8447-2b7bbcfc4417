package validation

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/gin-ai-backend/internal/errors"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

var validate *validator.Validate

func init() {
	validate = validator.New()
}

// ValidateStruct validates a struct using the validator tags
func ValidateStruct(s interface{}) error {
	return validate.Struct(s)
}

// HandleValidationErrors handles validation errors and returns them in a standardized format
func HandleValidationErrors(c *gin.Context, err error) {
	if err == nil {
		return
	}

	// Check if the error is a validation error
	validationErrors, ok := err.(validator.ValidationErrors)
	if !ok {
		errors.BadRequest(c, "Invalid request data", err)
		return
	}

	// Create a map of field errors
	fieldErrors := make(map[string]string)
	for _, e := range validationErrors {
		fieldErrors[toSnakeCase(e.Field())] = getErrorMessage(e)
	}

	// Return a 400 Bad Request with the validation errors
	c.JSON(400, gin.H{
		"code":    400,
		"message": "Validation failed",
		"errors":  fieldErrors,
	})
}

// getErrorMessage returns a human-readable error message for a validation error
func getErrorMessage(e validator.FieldError) string {
	switch e.Tag() {
	case "required":
		return "This field is required"
	case "email":
		return "Invalid email format"
	case "min":
		return fmt.Sprintf("Must be at least %s characters long", e.Param())
	case "max":
		return fmt.Sprintf("Must be at most %s characters long", e.Param())
	case "alphanum":
		return "Must contain only alphanumeric characters"
	case "url":
		return "Invalid URL format"
	default:
		return fmt.Sprintf("Failed validation for '%s'", e.Tag())
	}
}

// toSnakeCase converts a camelCase string to snake_case
func toSnakeCase(s string) string {
	var result strings.Builder
	for i, r := range s {
		if i > 0 && 'A' <= r && r <= 'Z' {
			result.WriteRune('_')
		}
		result.WriteRune(r)
	}
	return strings.ToLower(result.String())
}

// ValidateUsername validates a username
func ValidateUsername(username string) error {
	if len(username) < 3 {
		return fmt.Errorf("username must be at least 3 characters long")
	}
	if len(username) > 30 {
		return fmt.Errorf("username must be at most 30 characters long")
	}

	// Check if username contains only alphanumeric characters and underscores
	match, _ := regexp.MatchString("^[a-zA-Z0-9_]+$", username)
	if !match {
		return fmt.Errorf("username must contain only alphanumeric characters and underscores")
	}

	return nil
}

// ValidatePassword validates a password
func ValidatePassword(password string) error {
	if len(password) < 8 {
		return fmt.Errorf("password must be at least 8 characters long")
	}

	// Check if password contains at least one uppercase letter
	match, _ := regexp.MatchString("[A-Z]", password)
	if !match {
		return fmt.Errorf("password must contain at least one uppercase letter")
	}

	// Check if password contains at least one lowercase letter
	match, _ = regexp.MatchString("[a-z]", password)
	if !match {
		return fmt.Errorf("password must contain at least one lowercase letter")
	}

	// Check if password contains at least one digit
	match, _ = regexp.MatchString("[0-9]", password)
	if !match {
		return fmt.Errorf("password must contain at least one digit")
	}

	return nil
}

// ValidateEmail validates an email address
func ValidateEmail(email string) error {
	// Simple email validation
	match, _ := regexp.MatchString("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", email)
	if !match {
		return fmt.Errorf("invalid email format")
	}

	return nil
}
