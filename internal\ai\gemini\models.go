package gemini

// GenerateTextOptions represents configuration options for text generation requests
type GenerateTextOptions struct {
	// Model specifies which AI model to use (overrides the default)
	Model string
	// Temperature controls randomness (0-1, higher = more random)
	Temperature float32
	// MaxOutputTokens limits the maximum number of tokens in the response
	MaxOutputTokens int32
	// TopP controls diversity via nucleus sampling
	TopP float32
	// TopK controls diversity by limiting the number of token choices
	TopK int32
	// LogPrefix is prepended to log messages for this request
	LogPrefix string
}

// ChatMessage represents a single message in a conversation
type ChatMessage struct {
	// Role defines the message sender (user, assistant/model, system)
	Role string
	// Content contains the actual message text
	Content string
}

// ChatOptions represents configuration options for chat conversations
type ChatOptions struct {
	// Model specifies which AI model to use (overrides the default)
	Model string
	// Temperature controls randomness (0-1, higher = more random)
	Temperature float32
	// MaxOutputTokens limits the maximum number of tokens in the response
	MaxOutputTokens int32
	// TopP controls diversity via nucleus sampling
	TopP float32
	// <PERSON><PERSON> controls diversity by limiting the number of token choices
	TopK int32
	// History contains previous messages in the conversation
	History []ChatMessage
	// SystemPrompt provides instructions to the AI about how to behave
	SystemPrompt string
}

// StreamTextCallback is a callback function type for streaming text generation
// It receives each chunk of text as it's generated and a flag indicating completion
type StreamTextCallback func(text string, done bool)
