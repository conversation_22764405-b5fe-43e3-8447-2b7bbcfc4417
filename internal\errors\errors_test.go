package errors

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	return gin.New()
}

func TestRespondWithError(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		testErr := errors.New("test error details")
		RespondWithError(c, http.StatusBadRequest, "Test error message", testErr)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, response.Code)
	assert.Equal(t, "Test error message", response.Message)
	assert.Equal(t, "test error details", response.Details)
}

func TestRespondWithError_NoError(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		RespondWithError(c, http.StatusBadRequest, "Test error message", nil)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, response.Code)
	assert.Equal(t, "Test error message", response.Message)
	assert.Empty(t, response.Details)
}

func TestBadRequest(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		testErr := errors.New("validation failed")
		BadRequest(c, "Invalid input", testErr)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, response.Code)
	assert.Equal(t, "Invalid input", response.Message)
	assert.Equal(t, "validation failed", response.Details)
}

func TestUnauthorized(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		Unauthorized(c, "Custom unauthorized message", nil)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusUnauthorized, response.Code)
	assert.Equal(t, "Custom unauthorized message", response.Message)
}

func TestUnauthorized_DefaultMessage(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		Unauthorized(c, "", nil)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusUnauthorized, response.Code)
	assert.Equal(t, "Unauthorized", response.Message)
}

func TestForbidden(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		Forbidden(c, "Access denied", nil)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusForbidden, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusForbidden, response.Code)
	assert.Equal(t, "Access denied", response.Message)
}

func TestForbidden_DefaultMessage(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		Forbidden(c, "", nil)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusForbidden, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusForbidden, response.Code)
	assert.Equal(t, "Forbidden", response.Message)
}

func TestNotFound(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		NotFound(c, "User not found", nil)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusNotFound, response.Code)
	assert.Equal(t, "User not found", response.Message)
}

func TestNotFound_DefaultMessage(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		NotFound(c, "", nil)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusNotFound, response.Code)
	assert.Equal(t, "Resource not found", response.Message)
}

func TestConflict(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		Conflict(c, "Email already exists", nil)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusConflict, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusConflict, response.Code)
	assert.Equal(t, "Email already exists", response.Message)
}

func TestInternalServerError(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		testErr := errors.New("database connection failed")
		InternalServerError(c, "Database error", testErr)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusInternalServerError, response.Code)
	assert.Equal(t, "Database error", response.Message)
	assert.Equal(t, "database connection failed", response.Details)
}

func TestInternalServerError_DefaultMessage(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		InternalServerError(c, "", nil)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusInternalServerError, response.Code)
	assert.Equal(t, "Internal server error", response.Message)
}

func TestTooManyRequests(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		TooManyRequests(c, "Rate limit exceeded", nil)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusTooManyRequests, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusTooManyRequests, response.Code)
	assert.Equal(t, "Rate limit exceeded", response.Message)
}

func TestTooManyRequests_DefaultMessage(t *testing.T) {
	router := setupTestRouter()
	
	router.GET("/test", func(c *gin.Context) {
		TooManyRequests(c, "", nil)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusTooManyRequests, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusTooManyRequests, response.Code)
	assert.Equal(t, "Too many requests", response.Message)
}
