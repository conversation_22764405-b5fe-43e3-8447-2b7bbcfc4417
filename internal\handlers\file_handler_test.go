package handlers

import (
	"bytes"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-ai-backend/internal/storage"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// MockR2Client is a mock implementation of the storage.R2ClientInterface
type MockR2Client struct{}

// Ensure MockR2Client implements storage.R2ClientInterface
var _ storage.R2ClientInterface = (*MockR2Client)(nil)

func (m *MockR2Client) UploadFile(fileBytes []byte, fileName, contentType string) (string, error) {
	return "https://mocked-url.com/" + fileName, nil
}

func (m *MockR2Client) GetFile(fileName string) ([]byte, error) {
	return []byte("mock file content"), nil
}

func (m *MockR2Client) GeneratePublicURL(fileName string, expires time.Duration) (string, error) {
	return "https://mocked-public-url.com/" + fileName, nil
}

func (m *MockR2Client) DeleteFile(fileName string) error {
	return nil
}

func TestUploadFile(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.Default()

	handler := &FileHandler{R2Client: &MockR2Client{}}

	router.POST("/upload", func(c *gin.Context) {
		c.Set("userID", 1) // Simulate authenticated user
		handler.UploadFile(c)
	})

	// Create a proper multipart form request
	var buf bytes.Buffer
	writer := storage.NewMultipartWriter(&buf, "file", "test.txt", "text/plain", []byte("test file content"))
	err := writer.Close()
	assert.NoError(t, err)

	req, _ := http.NewRequest("POST", "/upload", &buf)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Validate response
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), "File uploaded successfully")
}
