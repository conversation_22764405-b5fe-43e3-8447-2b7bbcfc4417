package handlers

import (
	"fmt"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-ai-backend/internal/errors"
	"github.com/gin-ai-backend/internal/storage"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// FileHandler handles file upload and download operations
type FileHandler struct {
	R2Client storage.R2ClientInterface
}

// NewFileHandler creates a new file handler
func <PERSON>FileHandler(accessKey, secretKey, endpoint, bucketName string) (*FileHandler, error) {
	r2Client, err := storage.NewR2Client(accessKey, secretKey, endpoint, bucketName)
	if err != nil {
		return nil, fmt.Errorf("failed to create R2 client: %w", err)
	}

	return &FileHandler{
		R2Client: r2Client,
	}, nil
}

// UploadFile handles file uploads to Cloudflare R2
// @Summary Upload a file
// @Description Upload a file to Cloudflare R2 storage
// @Tags files
// @Accept multipart/form-data
// @Produce json
// @Security ApiKeyAuth
// @Param file formData file true "File to upload"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/files/upload [post]
func (h *FileHandler) UploadFile(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		errors.Unauthorized(c, "User not authenticated", nil)
		return
	}

	// Get the file from form data
	file, err := c.FormFile("file")
	if err != nil {
		errors.BadRequest(c, "No file uploaded", err)
		return
	}

	// Validate file size (max 10MB)
	if file.Size > 10*1024*1024 {
		errors.BadRequest(c, "File size exceeds the 10MB limit", nil)
		return
	}

	// Get file extension and validate
	fileExt := strings.ToLower(filepath.Ext(file.Filename))

	// Validate file type
	allowedExtensions := map[string]bool{
		// Image formats
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".gif":  true,
		".webp": true,
		".avif": true,

		// Document formats
		".pdf":  true,
		".doc":  true,
		".docx": true,
		".txt":  true,

		// Audio formats
		".mp3":  true,
		".wav":  true,
		".ogg":  true,
		".flac": true,
		".aac":  true,
	}

	if !allowedExtensions[fileExt] {
		errors.BadRequest(c, "File type not allowed. Allowed types: jpg, jpeg, png, gif, webp, avif, pdf, doc, docx, txt, mp3, wav, ogg, flac, aac", nil)
		return
	}

	// Generate unique filename
	fileName := fmt.Sprintf("%d/%s%s", userID, uuid.New().String(), fileExt)

	// Open the uploaded file
	openedFile, err := file.Open()
	if err != nil {
		errors.InternalServerError(c, "Failed to open uploaded file", err)
		return
	}
	defer openedFile.Close()

	// Read file contents
	fileBuffer := make([]byte, file.Size)
	_, err = openedFile.Read(fileBuffer)
	if err != nil {
		errors.InternalServerError(c, "Failed to read file", err)
		return
	}

	// Upload to R2
	fileURL, err := h.R2Client.UploadFile(fileBuffer, fileName, file.Header.Get("Content-Type"))
	if err != nil {
		errors.InternalServerError(c, "Failed to upload file", err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":  "File uploaded successfully",
		"fileName": fileName,
		"fileURL":  fileURL,
		"fileSize": file.Size,
	})
}

// GetFile handles file retrieval from Cloudflare R2
// @Summary Get a file
// @Description Get a file from Cloudflare R2 storage
// @Tags files
// @Produce json
// @Security ApiKeyAuth
// @Param fileName path string true "File name"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/files/{fileName} [get]
func (h *FileHandler) GetFile(c *gin.Context) {
	fileName := c.Param("fileName")
	if fileName == "" {
		errors.BadRequest(c, "File name is required", nil)
		return
	}

	// Generate a URL that expires in 1 hour
	fileURL, err := h.R2Client.GeneratePublicURL(fileName, time.Hour)
	if err != nil {
		errors.InternalServerError(c, "Failed to generate file URL", err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"fileURL": fileURL,
	})
}

// DeleteFile handles file deletion from Cloudflare R2
// @Summary Delete a file
// @Description Delete a file from Cloudflare R2 storage
// @Tags files
// @Produce json
// @Security ApiKeyAuth
// @Param fileName path string true "File name"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Failure 403 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/files/{fileName} [delete]
func (h *FileHandler) DeleteFile(c *gin.Context) {
	fileName := c.Param("fileName")
	if fileName == "" {
		errors.BadRequest(c, "File name is required", nil)
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		errors.Unauthorized(c, "User not authenticated", nil)
		return
	}

	// Check if the file belongs to the user (assuming files are stored with userID prefix)
	filePrefix := fmt.Sprintf("%d/", userID)

	// Use proper path checking instead of deprecated filepath.HasPrefix
	// This ensures we're checking if the path actually starts with the prefix
	if len(fileName) < len(filePrefix) || fileName[:len(filePrefix)] != filePrefix {
		errors.Forbidden(c, "You don't have permission to delete this file", nil)
		return
	}

	// Delete the file
	err := h.R2Client.DeleteFile(fileName)
	if err != nil {
		errors.InternalServerError(c, "Failed to delete file", err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "File deleted successfully",
	})
}
