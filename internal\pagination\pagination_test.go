package pagination

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// TestUser represents a test user model
type TestUser struct {
	ID   uint   `gorm:"primaryKey"`
	Name string `gorm:"size:100"`
}

func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// Migrate the schema
	db.AutoMigrate(&TestUser{})

	// Create test data
	users := []TestUser{
		{Name: "User 1"},
		{Name: "User 2"},
		{Name: "User 3"},
		{Name: "User 4"},
		{Name: "User 5"},
		{Name: "User 6"},
		{Name: "User 7"},
		{Name: "User 8"},
		{Name: "User 9"},
		{Name: "User 10"},
		{Name: "User 11"},
		{Name: "User 12"},
	}

	for _, user := range users {
		db.Create(&user)
	}

	return db
}

func setupPaginationTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	return gin.New()
}

func TestPaginate_DefaultValues(t *testing.T) {
	db := setupTestDB()
	router := setupPaginationTestRouter()

	router.GET("/users", func(c *gin.Context) {
		var users []TestUser
		count, err := Paginate(c, db.Model(&TestUser{}), &users)
		
		assert.NoError(t, err)
		assert.Equal(t, int64(12), count)
		assert.Len(t, users, 10) // Default page size
		
		c.JSON(200, gin.H{
			"users": users,
			"count": count,
		})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/users", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestPaginate_CustomPageSize(t *testing.T) {
	db := setupTestDB()
	router := setupPaginationTestRouter()

	router.GET("/users", func(c *gin.Context) {
		var users []TestUser
		count, err := Paginate(c, db.Model(&TestUser{}), &users)
		
		assert.NoError(t, err)
		assert.Equal(t, int64(12), count)
		assert.Len(t, users, 5) // Custom page size
		
		c.JSON(200, gin.H{
			"users": users,
			"count": count,
		})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/users?page_size=5", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestPaginate_SecondPage(t *testing.T) {
	db := setupTestDB()
	router := setupPaginationTestRouter()

	router.GET("/users", func(c *gin.Context) {
		var users []TestUser
		count, err := Paginate(c, db.Model(&TestUser{}), &users)
		
		assert.NoError(t, err)
		assert.Equal(t, int64(12), count)
		assert.Len(t, users, 2) // Remaining items on page 2
		
		c.JSON(200, gin.H{
			"users": users,
			"count": count,
		})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/users?page=2&page_size=10", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestPaginate_EmptyResult(t *testing.T) {
	db := setupTestDB()
	router := setupPaginationTestRouter()

	router.GET("/users", func(c *gin.Context) {
		var users []TestUser
		// Query for non-existent users
		count, err := Paginate(c, db.Model(&TestUser{}).Where("name = ?", "NonExistent"), &users)
		
		assert.NoError(t, err)
		assert.Equal(t, int64(0), count)
		assert.Len(t, users, 0)
		
		c.JSON(200, gin.H{
			"users": users,
			"count": count,
		})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/users", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestGetPaginationInfo_DefaultValues(t *testing.T) {
	router := setupPaginationTestRouter()

	router.GET("/info", func(c *gin.Context) {
		info := GetPaginationInfo(c, 25)
		
		assert.Equal(t, 1, info["current_page"])
		assert.Equal(t, 10, info["page_size"])
		assert.Equal(t, int64(25), info["total_items"])
		assert.Equal(t, 3, info["total_pages"]) // ceil(25/10) = 3
		
		c.JSON(200, info)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/info", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestGetPaginationInfo_CustomValues(t *testing.T) {
	router := setupPaginationTestRouter()

	router.GET("/info", func(c *gin.Context) {
		info := GetPaginationInfo(c, 50)
		
		assert.Equal(t, 2, info["current_page"])
		assert.Equal(t, 20, info["page_size"])
		assert.Equal(t, int64(50), info["total_items"])
		assert.Equal(t, 3, info["total_pages"]) // ceil(50/20) = 3
		
		c.JSON(200, info)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/info?page=2&page_size=20", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestGetPaginationInfo_ExactDivision(t *testing.T) {
	router := setupPaginationTestRouter()

	router.GET("/info", func(c *gin.Context) {
		info := GetPaginationInfo(c, 20)
		
		assert.Equal(t, 1, info["current_page"])
		assert.Equal(t, 10, info["page_size"])
		assert.Equal(t, int64(20), info["total_items"])
		assert.Equal(t, 2, info["total_pages"]) // 20/10 = 2
		
		c.JSON(200, info)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/info", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestGetPaginationInfo_ZeroItems(t *testing.T) {
	router := setupPaginationTestRouter()

	router.GET("/info", func(c *gin.Context) {
		info := GetPaginationInfo(c, 0)
		
		assert.Equal(t, 1, info["current_page"])
		assert.Equal(t, 10, info["page_size"])
		assert.Equal(t, int64(0), info["total_items"])
		assert.Equal(t, 0, info["total_pages"]) // ceil(0/10) = 0
		
		c.JSON(200, info)
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/info", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestPaginationQuery_Validation(t *testing.T) {
	router := setupPaginationTestRouter()

	router.GET("/test", func(c *gin.Context) {
		var pagination PaginationQuery
		err := c.ShouldBindQuery(&pagination)
		
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
		
		c.JSON(200, pagination)
	})

	// Test valid values
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test?page=2&page_size=20", nil)
	router.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// Test invalid page (less than 1)
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", "/test?page=0&page_size=20", nil)
	router.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// Test invalid page_size (greater than 100)
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", "/test?page=1&page_size=101", nil)
	router.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// Test invalid page_size (less than 1)
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", "/test?page=1&page_size=0", nil)
	router.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestPaginate_WithFilters(t *testing.T) {
	db := setupTestDB()
	router := setupPaginationTestRouter()

	router.GET("/users", func(c *gin.Context) {
		var users []TestUser
		// Filter users with names containing "1"
		query := db.Model(&TestUser{}).Where("name LIKE ?", "%1%")
		count, err := Paginate(c, query, &users)
		
		assert.NoError(t, err)
		assert.Equal(t, int64(3), count) // User 1, User 10, User 11, User 12
		
		c.JSON(200, gin.H{
			"users": users,
			"count": count,
		})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/users", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}
