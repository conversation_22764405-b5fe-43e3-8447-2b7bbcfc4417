package grok

// GenerateText generates text using the Grok API
//
// This is the main public API function for text generation with Grok.
// It uses the singleton instance of GrokService to handle the request.
//
// Parameters:
//   - prompt: The user query or input text
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - The generated text response
//   - Any error that occurred during the process
func GenerateText(prompt string, options *GenerateTextOptions) (string, error) {
	return GetGrokService().GenerateText(prompt, options)
}

// StreamText generates text using the Grok API in streaming mode
//
// This function allows receiving the generated text in chunks as they are produced.
// It uses the singleton instance of GrokService to handle the request.
//
// Parameters:
//   - prompt: The user query or input text
//   - callback: Function to call for each chunk of generated text
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - Any error that occurred during the process
func StreamText(prompt string, callback StreamTextCallback, options *GenerateTextOptions) error {
	return GetGrokService().StreamText(prompt, callback, options)
}

// CreateChatCompletion creates a chat completion using the Grok API
//
// This function allows more control over the conversation by providing the full message history.
// It uses the singleton instance of GrokService to handle the request.
//
// Parameters:
//   - messages: Array of messages representing the conversation history
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - The complete response from the API
//   - Any error that occurred during the process
func CreateChatCompletion(messages []GrokMessage, options *GenerateTextOptions) (*GrokResponseBody, error) {
	return GetGrokService().CreateChatCompletion(messages, options)
}
