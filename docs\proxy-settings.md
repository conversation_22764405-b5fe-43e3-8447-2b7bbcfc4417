# 代理设置指南

在开发过程中，经常需要设置代理来解决网络访问问题，特别是在访问国外资源时。本文档提供了在不同环境中设置代理（特别是 `127.0.0.1:7890`）的详细指南。

## 命令行代理设置

### Windows 命令行 (CMD)

在 Windows 命令提示符中设置 HTTP 和 HTTPS 代理：

```cmd
set HTTP_PROXY=http://127.0.0.1:7890
set HTTPS_PROXY=http://127.0.0.1:7890
```

### Windows PowerShell

在 PowerShell 中设置代理：

```powershell
$env:HTTP_PROXY = "http://127.0.0.1:7890"
$env:HTTPS_PROXY = "http://127.0.0.1:7890"
```

### Linux/macOS (Bash/Zsh)

在 Linux 或 macOS 的终端中设置代理：

```bash
export HTTP_PROXY=http://127.0.0.1:7890
export HTTPS_PROXY=http://127.0.0.1:7890
```

## 开发工具代理设置

### Git 代理设置

为 Git 设置 HTTP/HTTPS 代理：

```bash
git config --global http.proxy http://127.0.0.1:7890
git config --global https.proxy http://127.0.0.1:7890
```

取消 Git 代理设置：

```bash
git config --global --unset http.proxy
git config --global --unset https.proxy
```

### Go 代理设置

为 Go 命令设置代理（推荐使用国内镜像）：

```bash
# 设置 GOPROXY
go env -w GOPROXY=https://goproxy.cn,direct

# 同时设置 HTTP 代理
# Windows CMD
set HTTP_PROXY=http://127.0.0.1:7890
set HTTPS_PROXY=http://127.0.0.1:7890

# Windows PowerShell
$env:HTTP_PROXY = "http://127.0.0.1:7890"
$env:HTTPS_PROXY = "http://127.0.0.1:7890"

# Linux/macOS
export HTTP_PROXY=http://127.0.0.1:7890
export HTTPS_PROXY=http://127.0.0.1:7890
```

### npm 代理设置

为 Node.js 和 npm 设置代理：

```bash
npm config set proxy http://127.0.0.1:7890
npm config set https-proxy http://127.0.0.1:7890
```

取消 npm 代理设置：

```bash
npm config delete proxy
npm config delete https-proxy
```

### Yarn 代理设置

为 Yarn 设置代理：

```bash
yarn config set proxy http://127.0.0.1:7890
yarn config set https-proxy http://127.0.0.1:7890
```

### Docker 代理设置

为 Docker 设置代理：

在 Windows 上，编辑 `%USERPROFILE%\.docker\config.json`：
```json
{
  "proxies": {
    "default": {
      "httpProxy": "http://127.0.0.1:7890",
      "httpsProxy": "http://127.0.0.1:7890",
      "noProxy": "localhost,127.0.0.1"
    }
  }
}
```

在 Linux/macOS 上，编辑 `~/.docker/config.json` 文件，内容同上。

## 永久设置代理

### Windows 系统级代理

1. 打开系统属性（右键点击"此电脑"→属性）
2. 点击"高级系统设置"
3. 点击"环境变量"
4. 在"系统变量"部分，点击"新建"
5. 添加变量名 `HTTP_PROXY`，变量值 `http://127.0.0.1:7890`
6. 再次点击"新建"
7. 添加变量名 `HTTPS_PROXY`，变量值 `http://127.0.0.1:7890`
8. 点击"确定"保存设置

### Linux/macOS 系统级代理

编辑 `~/.bashrc` 或 `~/.zshrc` 文件（取决于您使用的 shell），添加以下内容：

```bash
# 设置代理
export HTTP_PROXY=http://127.0.0.1:7890
export HTTPS_PROXY=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890
export https_proxy=http://127.0.0.1:7890

# 可选：添加一个函数来快速开启/关闭代理
proxy_on() {
    export HTTP_PROXY=http://127.0.0.1:7890
    export HTTPS_PROXY=http://127.0.0.1:7890
    export http_proxy=http://127.0.0.1:7890
    export https_proxy=http://127.0.0.1:7890
    echo "代理已开启"
}

proxy_off() {
    unset HTTP_PROXY
    unset HTTPS_PROXY
    unset http_proxy
    unset https_proxy
    echo "代理已关闭"
}
```

保存文件后，运行 `source ~/.bashrc` 或 `source ~/.zshrc` 使设置生效。

## 验证代理设置

您可以使用以下命令验证代理是否生效：

```bash
# 使用 curl 检查
curl -v https://www.google.com

# 查看当前环境变量中的代理设置
# Windows CMD
echo %HTTP_PROXY%
echo %HTTPS_PROXY%

# Windows PowerShell
echo $env:HTTP_PROXY
echo $env:HTTPS_PROXY

# Linux/macOS
echo $HTTP_PROXY
echo $HTTPS_PROXY
```

如果代理设置正确，您应该能看到请求通过 127.0.0.1:7890 发送。

## 常见问题解决

### 代理无效

1. 确认代理服务器（如 Clash、V2Ray 等）正在运行
2. 验证代理端口是否正确（默认可能是 7890、1080 等）
3. 尝试在浏览器中设置相同的代理，测试是否可以访问外网

### 特定工具不遵循系统代理

某些工具可能不遵循系统环境变量中的代理设置，需要单独配置。参考上面针对特定工具的代理设置说明。

### 代理影响本地开发

如果代理影响了本地开发（如访问 localhost），可以在代理设置中添加 no_proxy 变量：

```bash
# Windows
set NO_PROXY=localhost,127.0.0.1,::1

# Linux/macOS
export NO_PROXY=localhost,127.0.0.1,::1
```

## 在本项目中使用代理

在 gin-ai-backend 项目中，您可能需要设置代理来下载依赖或访问外部 API。建议按照以下步骤操作：

1. 设置 Go 模块代理：
   ```bash
   go env -w GOPROXY=https://goproxy.cn,direct
   ```

2. 如果仍然需要 HTTP 代理，设置环境变量：
   ```bash
   # Windows
   set HTTP_PROXY=http://127.0.0.1:7890
   set HTTPS_PROXY=http://127.0.0.1:7890
   
   # Linux/macOS
   export HTTP_PROXY=http://127.0.0.1:7890
   export HTTPS_PROXY=http://127.0.0.1:7890
   ```

3. 运行 Go 命令，如：
   ```bash
   go mod tidy
   go get -u github.com/swaggo/swag/cmd/swag
   ```

4. 对于 Docker 构建，可以在 Dockerfile 中添加代理设置：
   ```dockerfile
   FROM golang:1.24 as builder
   
   # 设置代理
   ENV GOPROXY=https://goproxy.cn,direct
   ENV HTTP_PROXY=http://host.docker.internal:7890
   ENV HTTPS_PROXY=http://host.docker.internal:7890
   
   # ... 其余构建步骤
   ```

注意：在 Docker 中，使用 `host.docker.internal` 而不是 `127.0.0.1` 来引用宿主机上的代理服务。
