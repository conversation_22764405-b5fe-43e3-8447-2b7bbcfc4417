name: Build and Deploy

on:
  push:
    branches: [ main, master, dev ]
  pull_request:
    branches: [ main, master, dev ]
  workflow_dispatch:  # Enable manual triggering

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history and tags

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.24'
          cache: true

  build:
    needs: lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history and tags

      - name: Generate Version Information
        id: version
        run: |
          # Debug: List all tags
          echo "All tags:"
          git tag -l

          # Get the latest tag or use v0.0.0 if no tags exist
          LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
          echo "Latest tag: $LATEST_TAG"

          # Extract version components
          VERSION=${LATEST_TAG#v}
          IFS='.' read -r MAJOR MINOR PATCH <<< "$VERSION"

          # Count commits since the latest tag
          COMMITS=$(git rev-list --count $LATEST_TAG..HEAD 2>/dev/null || echo "0")

          # Generate version code (MAJOR * 10000 + MINOR * 100 + PATCH + COMMITS)
          VERSION_CODE=$((MAJOR * 10000 + MINOR * 100 + PATCH + COMMITS))

          # Generate version name
          if [ "$GITHUB_REF_NAME" = "main" ] || [ "$GITHUB_REF_NAME" = "master" ]; then
              # For main/master branch, use the tag version with commit count if any
              if [ "$COMMITS" -gt "0" ]; then
                  VERSION_NAME="$MAJOR.$MINOR.$((PATCH + COMMITS))"
              else
                  VERSION_NAME="$MAJOR.$MINOR.$PATCH"
              fi
          else
              # For other branches, append branch name and commit count
              BRANCH_NAME=${GITHUB_REF_NAME//\//-}
              VERSION_NAME="$MAJOR.$MINOR.$PATCH-$BRANCH_NAME.$COMMITS"
          fi

          echo "VERSION_CODE=$VERSION_CODE" >> $GITHUB_ENV
          echo "VERSION_NAME=$VERSION_NAME" >> $GITHUB_ENV

          echo "Generated version code: $VERSION_CODE"
          echo "Generated version name: $VERSION_NAME"

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.24'
          cache: true

      - name: Get dependencies
        run: |
          go mod download
          go mod tidy

      - name: Build
        run: go build -v -o gin-ai-backend ./cmd
        env:
          VERSION: ${{ env.VERSION_NAME }}

  docker:
    needs: build
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history and tags

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # 暂时注释掉上传到 GitHub Container Registry 的步骤
      # - name: Log in to GitHub Container Registry
      #   uses: docker/login-action@v3
      #   with:
      #     registry: ghcr.io
      #     username: ${{ github.actor }}
      #     password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: gin-ai-backend # 修改为本地镜像名称，不再使用 ghcr.io 路径
          tags: |
            type=raw,value=${{ env.VERSION_NAME }}
            type=ref,event=branch
            type=sha,format=short
            latest

      - name: Build Docker image (without pushing)
        uses: docker/build-push-action@v5
        with:
          context: .
          push: false # 不推送镜像，只构建
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          # 输出到本地，便于后续使用
          outputs: type=docker,dest=/tmp/gin-ai-backend.tar
          build-args: |
            VERSION=${{ env.VERSION_NAME }}
            BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')

      # 保存构建的镜像为工作流程构件
      - name: Upload image as artifact
        uses: actions/upload-artifact@v4
        with:
          name: gin-ai-backend-image
          path: /tmp/gin-ai-backend.tar
          retention-days: 1