package middleware

import (
	"net/http"
	"runtime/debug"

	"github.com/gin-ai-backend/internal/errors"
	"github.com/gin-ai-backend/internal/logging"
	"github.com/gin-gonic/gin"
)

// ErrorHandlerMiddleware handles panics and returns a 500 error
func ErrorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if r := recover(); r != nil {
				// Log the error and stack trace
				logging.Error("Panic recovered", map[string]interface{}{
					"error":      r,
					"stackTrace": string(debug.Stack()),
					"path":       c.Request.URL.Path,
					"method":     c.Request.Method,
					"clientIP":   c.ClientIP(),
				})

				// Return a 500 error
				errors.InternalServerError(c, "Internal server error", nil)
				c.Abort()
			}
		}()

		c.Next()
	}
}

// NotFoundHandler handles 404 errors
func NotFoundHandler(c *gin.Context) {
	errors.NotFound(c, "Resource not found", nil)
}

// MethodNotAllowedHandler handles 405 errors
func MethodNotAllowedHandler(c *gin.Context) {
	errors.RespondWithError(c, http.StatusMethodNotAllowed, "Method not allowed", nil)
}
