package handlers

import (
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/gin-ai-backend/internal/errors"
	"github.com/gin-ai-backend/internal/media"
	"github.com/gin-ai-backend/internal/storage"
	"github.com/gin-gonic/gin"
)

// MediaHandler handles media processing operations
type MediaHandler struct {
	R2Client       storage.R2ClientInterface
	MediaProcessor *media.MediaProcessor
}

// NewMediaHandler creates a new media handler
func NewMediaHandler(r2Client storage.R2ClientInterface, tempDir string) (*MediaHandler, error) {
	mediaProcessor, err := media.NewMediaProcessor(tempDir)
	if err != nil {
		return nil, fmt.Errorf("failed to create media processor: %w", err)
	}

	return &MediaHandler{
		R2Client:       r2Client,
		MediaProcessor: mediaProcessor,
	}, nil
}

// ConvertImage handles image format conversion
// @Summary Convert image format
// @Description Convert an image from one format to another
// @Tags media
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param fileName path string true "Original file name"
// @Param format query string true "Target format (jpeg, png, gif, webp, avif)"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/media/convert/image/{fileName} [get]
func (h *MediaHandler) ConvertImage(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		errors.Unauthorized(c, "User not authenticated", nil)
		return
	}

	// Get file name from path parameter
	fileName := c.Param("fileName")
	if fileName == "" {
		errors.BadRequest(c, "File name is required", nil)
		return
	}

	// Check if the file belongs to the user
	filePrefix := fmt.Sprintf("%d/", userID)
	if len(fileName) < len(filePrefix) || fileName[:len(filePrefix)] != filePrefix {
		errors.Forbidden(c, "You don't have permission to access this file", nil)
		return
	}

	// Get target format from query parameter
	targetFormatStr := c.Query("format")
	if targetFormatStr == "" {
		errors.BadRequest(c, "Target format is required", nil)
		return
	}

	// Validate target format
	validFormats := map[string]media.ImageFormat{
		"jpeg": media.JPEG,
		"png":  media.PNG,
		"gif":  media.GIF,
		"webp": media.WEBP,
		"avif": media.AVIF,
	}

	targetFormat, valid := validFormats[strings.ToLower(targetFormatStr)]
	if !valid {
		errors.BadRequest(c, "Invalid target format. Supported formats: jpeg, png, gif, webp, avif", nil)
		return
	}

	// Get original file from R2
	fileData, err := h.R2Client.GetFile(fileName)
	if err != nil {
		errors.InternalServerError(c, "Failed to get file", err)
		return
	}

	// Determine source format
	sourceFormat := media.GetImageFormat(fileName)

	// Convert image
	convertedData, err := h.MediaProcessor.ConvertImageFormat(fileData, sourceFormat, targetFormat)
	if err != nil {
		errors.InternalServerError(c, "Failed to convert image", err)
		return
	}

	// Generate new file name
	fileExt := fmt.Sprintf(".%s", targetFormat)
	newFileName := strings.TrimSuffix(fileName, filepath.Ext(fileName)) + fileExt

	// Upload converted file to R2
	contentType := fmt.Sprintf("image/%s", targetFormat)
	fileURL, err := h.R2Client.UploadFile(convertedData, newFileName, contentType)
	if err != nil {
		errors.InternalServerError(c, "Failed to upload converted file", err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Image converted successfully",
		"fileName":   newFileName,
		"fileURL":    fileURL,
		"fileSize":   len(convertedData),
		"fromFormat": sourceFormat,
		"toFormat":   targetFormat,
	})
}

// ResizeImage handles image resizing
// @Summary Resize image
// @Description Resize an image to specified dimensions
// @Tags media
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param fileName path string true "File name"
// @Param width query int true "Target width"
// @Param height query int true "Target height"
// @Param preserve query bool false "Preserve aspect ratio (default: true)"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/media/resize/image/{fileName} [get]
func (h *MediaHandler) ResizeImage(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		errors.Unauthorized(c, "User not authenticated", nil)
		return
	}

	// Get file name from path parameter
	fileName := c.Param("fileName")
	if fileName == "" {
		errors.BadRequest(c, "File name is required", nil)
		return
	}

	// Check if the file belongs to the user
	filePrefix := fmt.Sprintf("%d/", userID)
	if len(fileName) < len(filePrefix) || fileName[:len(filePrefix)] != filePrefix {
		errors.Forbidden(c, "You don't have permission to access this file", nil)
		return
	}

	// Get width and height from query parameters
	widthStr := c.Query("width")
	heightStr := c.Query("height")
	preserveStr := c.DefaultQuery("preserve", "true")

	width, err := strconv.Atoi(widthStr)
	if err != nil || width <= 0 {
		errors.BadRequest(c, "Invalid width parameter", err)
		return
	}

	height, err := strconv.Atoi(heightStr)
	if err != nil || height <= 0 {
		errors.BadRequest(c, "Invalid height parameter", err)
		return
	}

	preserve := preserveStr == "true"

	// Get original file from R2
	fileData, err := h.R2Client.GetFile(fileName)
	if err != nil {
		errors.InternalServerError(c, "Failed to get file", err)
		return
	}

	// Determine image format
	format := media.GetImageFormat(fileName)

	// Resize image
	resizeOptions := media.ResizeOptions{
		Width:    width,
		Height:   height,
		Preserve: preserve,
	}

	resizedData, err := h.MediaProcessor.ResizeImage(fileData, format, resizeOptions)
	if err != nil {
		errors.InternalServerError(c, "Failed to resize image", err)
		return
	}

	// Generate new file name
	fileExt := filepath.Ext(fileName)
	baseName := strings.TrimSuffix(fileName, fileExt)
	newFileName := fmt.Sprintf("%s_%dx%d%s", baseName, width, height, fileExt)

	// Upload resized file to R2
	contentType := fmt.Sprintf("image/%s", strings.TrimPrefix(fileExt, "."))
	fileURL, err := h.R2Client.UploadFile(resizedData, newFileName, contentType)
	if err != nil {
		errors.InternalServerError(c, "Failed to upload resized file", err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":  "Image resized successfully",
		"fileName": newFileName,
		"fileURL":  fileURL,
		"fileSize": len(resizedData),
		"width":    width,
		"height":   height,
	})
}

// ConvertAudio handles audio format conversion
// @Summary Convert audio format
// @Description Convert an audio file from one format to another
// @Tags media
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param fileName path string true "Original file name"
// @Param format query string true "Target format (mp3, wav, ogg, flac, aac)"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/media/convert/audio/{fileName} [get]
func (h *MediaHandler) ConvertAudio(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		errors.Unauthorized(c, "User not authenticated", nil)
		return
	}

	// Get file name from path parameter
	fileName := c.Param("fileName")
	if fileName == "" {
		errors.BadRequest(c, "File name is required", nil)
		return
	}

	// Check if the file belongs to the user
	filePrefix := fmt.Sprintf("%d/", userID)
	if len(fileName) < len(filePrefix) || fileName[:len(filePrefix)] != filePrefix {
		errors.Forbidden(c, "You don't have permission to access this file", nil)
		return
	}

	// Get target format from query parameter
	targetFormatStr := c.Query("format")
	if targetFormatStr == "" {
		errors.BadRequest(c, "Target format is required", nil)
		return
	}

	// Validate target format
	validFormats := map[string]media.AudioFormat{
		"mp3":  media.MP3,
		"wav":  media.WAV,
		"ogg":  media.OGG,
		"flac": media.FLAC,
		"aac":  media.AAC,
	}

	targetFormat, valid := validFormats[strings.ToLower(targetFormatStr)]
	if !valid {
		errors.BadRequest(c, "Invalid target format. Supported formats: mp3, wav, ogg, flac, aac", nil)
		return
	}

	// Get original file from R2
	fileData, err := h.R2Client.GetFile(fileName)
	if err != nil {
		errors.InternalServerError(c, "Failed to get file", err)
		return
	}

	// Determine source format
	sourceFormat := media.GetAudioFormat(fileName)

	// Convert audio
	convertedData, err := h.MediaProcessor.ConvertAudioFormat(fileData, sourceFormat, targetFormat)
	if err != nil {
		errors.InternalServerError(c, "Failed to convert audio", err)
		return
	}

	// Generate new file name
	fileExt := fmt.Sprintf(".%s", targetFormat)
	newFileName := strings.TrimSuffix(fileName, filepath.Ext(fileName)) + fileExt

	// Upload converted file to R2
	contentType := fmt.Sprintf("audio/%s", targetFormat)
	fileURL, err := h.R2Client.UploadFile(convertedData, newFileName, contentType)
	if err != nil {
		errors.InternalServerError(c, "Failed to upload converted file", err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Audio converted successfully",
		"fileName":   newFileName,
		"fileURL":    fileURL,
		"fileSize":   len(convertedData),
		"fromFormat": sourceFormat,
		"toFormat":   targetFormat,
	})
}
