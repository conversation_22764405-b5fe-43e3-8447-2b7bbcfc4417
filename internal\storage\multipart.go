package storage

import (
	"bytes"
	"io"
	"mime/multipart"
)

// MultipartWriter is a wrapper around multipart.Writer for easier testing
type MultipartWriter struct {
	writer *multipart.Writer
}

// NewMultipartWriter creates a new multipart writer and adds a file to it
func NewMultipartWriter(buf *bytes.Buffer, fieldName, fileName, contentType string, fileContent []byte) *MultipartWriter {
	writer := multipart.NewWriter(buf)

	// Create a form file field
	fileWriter, _ := writer.CreateFormFile(fieldName, fileName)

	// Write the file content
	io.Copy(fileWriter, bytes.NewReader(fileContent))

	return &MultipartWriter{
		writer: writer,
	}
}

// FormDataContentType returns the content type for the multipart form
func (m *MultipartWriter) FormDataContentType() string {
	return m.writer.FormDataContentType()
}

// Close closes the multipart writer
func (m *MultipartWriter) Close() error {
	return m.writer.Close()
}
