# gin-ai-backend

**gin-ai-backend** is a RESTful API backend service built with Go and the Gin framework, designed specifically for AI functionality. The project is containerized with Docker to ensure consistent deployment across different environments.

## Project Overview

This project aims to provide a high-performance, easy-to-deploy backend service that supports multiple AI providers (OpenAI, DeepSeek, XAI, Google Gemini), multiple database types (SQLite, Supabase, Neon), authentication, file storage with Cloudflare R2, and more. It uses the Gin framework for fast API development and Docker for containerized deployment.

## Technology Stack

- **Language**: Go
- **Web Framework**: Gin
- **Authentication**: JWT
- **Databases**:
  - SQLite (via GORM)
  - PostgreSQL (via GORM) for Supabase and Neon
- **Storage**: Cloudflare R2
- **AI Integration**:
  - OpenAI API
  - DeepSeek API
  - XAI API
  - Google Gemini API
- **API Documentation**: Swagger/OpenAPI
- **Error Handling**: Standardized error responses
- **Rate Limiting**: Configurable rate limits for API endpoints
- **Input Validation**: Comprehensive input validation
- **Caching**: In-memory caching for AI responses
- **Pagination**: Paginated endpoints for list responses
- **Structured Logging**: Detailed logging with context
- **Security Headers**: CORS, CSP, and other security headers
- **Containerization**: Docker

## Quick Start

### 1. Clone the repository

```bash
git clone https://github.com/your-username/gin-ai-backend.git
cd gin-ai-backend
```

### 2. Create a .env file

```bash
# Server Configuration
SERVER_PORT=8080
JWT_SECRET=your-secret-key

# Database Configuration
DATABASE_TYPE=sqlite  # Options: sqlite, supabase, neon
DATABASE_URL=         # PostgreSQL connection string for Supabase or Neon
DATABASE_PATH=database.db  # For SQLite

# AI API Configuration
OPENAI_API_KEY=your-openai-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key
XAI_API_KEY=your-xai-api-key
GEMINI_API_KEY=your-gemini-api-key
DEFAULT_AI_MODEL=openai  # Options: openai, deepseek, xai, gemini

# R2 Storage Configuration
R2_ACCESS_KEY=your-r2-access-key
R2_SECRET_KEY=your-r2-secret-key
R2_ENDPOINT=https://your-account.r2.cloudflarestorage.com
R2_BUCKET=your-bucket-name
```

### 3. Install dependencies

```bash
go mod tidy
```

### 4. Run the service

```bash
go run cmd/main.go
```

The service will run on `http://localhost:8080`.

### 5. Build and run with Docker

```bash
docker build -t gin-ai-backend .
docker run -p 8080:8080 --env-file .env gin-ai-backend
```

## Project Structure

```
gin-ai-backend/
  ├── .github/                 # GitHub configuration
  │   └── workflows/          # GitHub Actions workflows
  │       └── ci-cd.yml       # CI/CD pipeline configuration
  ├── cmd/                     # Project entry points
  │   └── main.go             # Main program entry file
  ├── internal/                # Internal modules (not exposed externally)
  │   ├── ai/                 # AI module
  │   │   └── ai.go          # AI-related logic for multiple providers
  │   ├── auth/               # Authentication and authorization
  │   │   └── auth.go        # Token generation and verification
  │   ├── cache/              # Caching implementation
  │   │   └── cache.go       # In-memory cache for AI responses
  │   ├── database/           # Database connections and operations
  │   │   └── database.go    # Database initialization for multiple DB types
  │   ├── errors/             # Error handling
  │   │   └── errors.go      # Standardized error responses
  │   ├── handlers/           # API handlers
  │   │   ├── ai_handler.go  # AI-related APIs
  │   │   ├── auth_handler.go # Authentication APIs
  │   │   └── file_handler.go # File upload APIs
  │   ├── logging/            # Structured logging
  │   │   └── logging.go     # Logging implementation
  │   ├── middleware/         # Middleware
  │   │   ├── auth_middleware.go # Authentication middleware
  │   │   ├── error_middleware.go # Error handling middleware
  │   │   ├── logging_middleware.go # Logging middleware
  │   │   ├── rate_limit_middleware.go # Rate limiting middleware
  │   │   └── security_middleware.go # Security headers middleware
  │   ├── models/             # Data models
  │   │   └── user.go        # User model definitions
  │   ├── pagination/         # Pagination utilities
  │   │   └── pagination.go  # Pagination implementation
  │   ├── storage/            # File storage (Cloudflare R2)
  │   │   └── r2.go          # R2 upload and access logic
  │   └── validation/         # Input validation
  │       └── validation.go  # Validation utilities
  ├── pkg/                     # Reusable packages (optional)
  ├── docs/                     # Swagger/OpenAPI documentation
  │   ├── swagger.go           # Swagger configuration
  │   └── docs.go              # Generated Swagger documentation
  ├── config/                  # Configuration files
  │   └── config.go           # Environment variables and config management
  ├── go.mod                   # Go module file
  ├── go.sum                   # Dependency checksum file
  ├── Dockerfile               # Docker build file
  └── README.md                # Project documentation
```

## API Documentation

When the server is running, you can access the Swagger UI at:

```
http://localhost:8080/swagger/index.html
```

### Authentication Endpoints

- `POST /auth/register` - Register a new user
- `POST /auth/login` - Login with username and password
- `GET /auth/profile` - Get user profile (requires authentication)

### AI Endpoints

- `POST /api/ai/generate` - Generate AI response (requires authentication)
  - Query parameter `provider` - Optional AI provider to use (openai, deepseek, xai, gemini)
  - Request body:
    ```json
    {
      "prompt": "Your prompt here",
      "provider": "openai" // Optional
    }
    ```
- `GET /api/ai/providers` - Get available AI providers (requires authentication)

### File Storage Endpoints

- `POST /api/files/upload` - Upload a file to R2 (requires authentication)
- `GET /api/files/:fileName` - Get a file from R2 (requires authentication)
- `DELETE /api/files/:fileName` - Delete a file from R2 (requires authentication)

### Admin Endpoints

- `GET /admin/` - Admin area (requires admin role)
- `GET /admin/database/info` - Database information (requires admin role)
- `GET /admin/users` - List users with pagination (requires admin role)

## Database Support

The application supports the following database types:

1. **SQLite** - Default, lightweight database for development
2. **Supabase** - PostgreSQL-compatible managed database service
3. **Neon** - Serverless PostgreSQL database service

Configure the database type using the `DATABASE_TYPE` environment variable and provide connection details as needed.

## AI Provider Support

The application supports the following AI providers:

1. **OpenAI** - Uses the OpenAI Chat API
2. **DeepSeek** - Integrates with DeepSeek AI models
3. **XAI** - Connects to the XAI API service
4. **Google Gemini** - Leverages Google's Gemini AI models

Configure each provider with its respective API key in the environment variables, and set the default model using `DEFAULT_AI_MODEL`.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request or create an Issue to improve the project.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
