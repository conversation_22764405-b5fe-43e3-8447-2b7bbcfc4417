package grok

// GrokMessage represents a message for the Grok API
type GrokMessage struct {
	// Role defines the message sender (system, user, assistant)
	Role string `json:"role"`
	// Content contains the actual message text
	Content string `json:"content"`
}

// GrokRequestBody represents the request body for the Grok API
type GrokRequestBody struct {
	// Model specifies which AI model to use
	Model string `json:"model"`
	// Messages contains the conversation history
	Messages []GrokMessage `json:"messages"`
	// Temperature controls randomness (0-1, higher = more random)
	Temperature float64 `json:"temperature,omitempty"`
	// MaxTokens limits the maximum number of tokens in the response
	MaxTokens int `json:"max_tokens,omitempty"`
	// TopP controls diversity via nucleus sampling
	TopP float64 `json:"top_p,omitempty"`
	// Stream indicates whether to use streaming mode for the response
	Stream bool `json:"stream,omitempty"`
}

// GrokChoice represents a choice in the Grok API response
type GrokChoice struct {
	// Index is the position of this choice in the array of choices
	Index int `json:"index"`
	// Message contains the response content
	Message GrokMessage `json:"message"`
	// FinishReason indicates why the model stopped generating text
	FinishReason string `json:"finish_reason"`
}

// GrokResponseBody represents the response body from the Grok API
type GrokResponseBody struct {
	// ID is the unique identifier for this response
	ID string `json:"id"`
	// Object indicates the type of object returned
	Object string `json:"object"`
	// Created is the Unix timestamp when the response was generated
	Created int64 `json:"created"`
	// Model is the name of the model used to generate the response
	Model string `json:"model"`
	// Choices contains the array of generated responses
	Choices []GrokChoice `json:"choices"`
}

// GenerateTextOptions represents options for text generation
type GenerateTextOptions struct {
	// Model specifies which AI model to use (overrides the default)
	Model string
	// SystemPrompt provides instructions to the AI about how to behave
	SystemPrompt string
	// Temperature controls randomness (0-1, higher = more random)
	Temperature float64
	// MaxTokens limits the maximum number of tokens in the response
	MaxTokens int
	// TopP controls diversity via nucleus sampling
	TopP float64
	// FrequencyPenalty reduces repetition of token sequences
	FrequencyPenalty float64
	// PresencePenalty reduces repetition of topics
	PresencePenalty float64
	// LogPrefix is prepended to log messages for this request
	LogPrefix string
}

// StreamTextCallback is a callback function type for streaming text generation
// It receives each chunk of text as it's generated and a flag indicating completion
type StreamTextCallback func(text string, done bool)
