package middleware

import (
	"sync"
	"time"

	"github.com/gin-ai-backend/internal/errors"
	"github.com/gin-gonic/gin"
)

// RateLimiter implements a simple in-memory rate limiter
type RateLimiter struct {
	mutex    sync.Mutex
	requests map[string][]time.Time
	window   time.Duration
	limit    int
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(window time.Duration, limit int) *RateLimiter {
	return &RateLimiter{
		requests: make(map[string][]time.Time),
		window:   window,
		limit:    limit,
	}
}

// Allow checks if a request is allowed based on the rate limit
func (rl *RateLimiter) Allow(key string) bool {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()

	// Clean up old requests
	if _, exists := rl.requests[key]; exists {
		var validRequests []time.Time
		for _, t := range rl.requests[key] {
			if now.Sub(t) <= rl.window {
				validRequests = append(validRequests, t)
			}
		}
		rl.requests[key] = validRequests
	} else {
		rl.requests[key] = []time.Time{}
	}

	// Check if the request is allowed
	if len(rl.requests[key]) >= rl.limit {
		return false
	}

	// Add the current request
	rl.requests[key] = append(rl.requests[key], now)
	return true
}

// RateLimitMiddleware creates a middleware that limits requests based on IP or user ID
func RateLimitMiddleware(window time.Duration, limit int) gin.HandlerFunc {
	limiter := NewRateLimiter(window, limit)

	return func(c *gin.Context) {
		// Get identifier (user ID or IP)
		var identifier string
		userID, exists := c.Get("userID")
		if exists {
			identifier = "user:" + userID.(string)
		} else {
			identifier = "ip:" + c.ClientIP()
		}

		// Check rate limit
		if !limiter.Allow(identifier) {
			errors.TooManyRequests(c, "Rate limit exceeded. Please try again later.", nil)
			c.Abort()
			return
		}

		c.Next()
	}
}
