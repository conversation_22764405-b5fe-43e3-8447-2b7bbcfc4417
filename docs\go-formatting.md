# Go 代码格式化指南

Go 语言以其严格的代码格式化规范而闻名，这有助于保持代码库的一致性和可读性。本文档介绍了在 gin-ai-backend 项目中格式化 Go 代码的各种方法和工具。

## 1. 使用 `go fmt` 命令

Go 提供了内置的 `go fmt` 命令，这是最基本的代码格式化工具：

```bash
# 格式化当前目录下的所有 Go 文件
go fmt ./...

# 格式化特定文件
go fmt filename.go
```

`go fmt` 实际上是 `gofmt -l -w` 的简单封装，它会重写源文件以符合标准格式。

## 2. 使用 `gofmt` 命令

`gofmt` 是 `go fmt` 的底层工具，提供了更多选项：

```bash
# 格式化并覆盖原文件
gofmt -w filename.go

# 格式化整个目录
gofmt -w .

# 显示格式化前后的差异
gofmt -d filename.go

# 简化代码（例如，删除不必要的括号）
gofmt -s -w filename.go
```

## 3. 使用 `goimports`

`goimports` 是 `gofmt` 的增强版，除了格式化代码外，还会自动添加或删除导入的包：

```bash
# 安装 goimports
go install golang.org/x/tools/cmd/goimports@latest

# 使用 goimports 格式化文件
goimports -w filename.go

# 格式化整个项目
goimports -w .
```

`goimports` 是推荐的格式化工具，因为它不仅可以格式化代码，还可以管理导入语句，避免常见的 "unused import" 和 "undefined: xxx" 错误。

## 4. 使用 `golangci-lint`

`golangci-lint` 是一个综合性的 Go 代码检查工具，不仅可以格式化代码，还可以检查代码质量：

```bash
# 安装 golangci-lint
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 运行 lint 检查
golangci-lint run

# 自动修复问题
golangci-lint run --fix
```

在项目根目录创建 `.golangci.yml` 配置文件可以自定义 lint 规则：

```yaml
linters:
  enable:
    - gofmt
    - goimports
    - govet
    - staticcheck
    - unused
    - gosimple
    - ineffassign
    - misspell
```

## 5. 在 Dockerfile 中添加格式化步骤

如果你想在构建 Docker 镜像时格式化代码，可以在 Dockerfile 中添加相应的步骤：

```dockerfile
# 在构建阶段添加格式化步骤
FROM golang:1.24 as builder

WORKDIR /app

# 复制 go.mod 和 go.sum 文件
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 安装 goimports 并格式化代码
RUN go install golang.org/x/tools/cmd/goimports@latest
RUN goimports -w .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -o main ./cmd/main.go
```

## 6. 使用 IDE/编辑器自动格式化

大多数支持 Go 的 IDE 和编辑器都提供了保存时自动格式化的功能：

### VS Code

1. 安装 Go 扩展
2. 在设置中启用 "Format On Save"
3. 确保 "Go: Format Tool" 设置为 "goimports"

```json
{
  "editor.formatOnSave": true,
  "go.formatTool": "goimports"
}
```

### GoLand

GoLand 默认支持保存时格式化，可以在设置中配置：

1. 转到 `Settings` > `Tools` > `File Watchers`
2. 添加 `goimports` 文件监视器

### Vim/Neovim

使用 vim-go 插件配置保存时格式化：

```vim
" 在 .vimrc 或 init.vim 中添加
Plug 'fatih/vim-go', { 'do': ':GoUpdateBinaries' }
let g:go_fmt_command = "goimports"
let g:go_fmt_autosave = 1
```

## 7. 使用 Git 钩子自动格式化

你可以设置 Git pre-commit 钩子，在提交代码前自动格式化：

```bash
# 创建 pre-commit 钩子
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/sh
files=$(git diff --cached --name-only --diff-filter=ACM | grep '\.go$')
if [ -n "$files" ]; then
    goimports -w $files
    git add $files
fi
EOF

# 使钩子可执行
chmod +x .git/hooks/pre-commit
```

## 8. 在项目中添加 Makefile

你可以在项目中添加 Makefile，包含格式化命令：

```makefile
.PHONY: fmt
fmt:
	go fmt ./...
	goimports -w .

.PHONY: lint
lint:
	golangci-lint run

.PHONY: lint-fix
lint-fix:
	golangci-lint run --fix
```

然后运行 `make fmt` 来格式化代码。

## 项目推荐的格式化方法

对于 gin-ai-backend 项目，我们推荐以下格式化流程：

1. 在开发环境中配置编辑器自动格式化（使用 goimports）
2. 在项目中添加 Makefile 以便快速格式化
3. 使用 golangci-lint 进行更全面的代码质量检查
4. 考虑添加 Git pre-commit 钩子以确保提交前代码已格式化

## 格式化规范

Go 的格式化规范主要包括：

- 使用制表符（tab）而非空格进行缩进
- 行长度没有硬性限制，但通常保持在 80-120 个字符以内
- 花括号始终使用 K&R 风格（左括号不换行）
- 导入包按字母顺序分组排列
- 不使用分号作为语句结束符
- 命名使用驼峰式（CamelCase），不使用下划线
- 公共函数/变量使用大写字母开头，私有函数/变量使用小写字母开头

遵循这些格式化规范和使用推荐的工具，可以确保代码库的一致性和可读性，提高团队协作效率。
