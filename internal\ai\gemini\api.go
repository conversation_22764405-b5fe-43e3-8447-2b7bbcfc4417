package gemini

// GenerateText generates text using the Gemini API
//
// This is the main public API function for text generation with Gemini.
// It uses the singleton instance of GeminiService to handle the request.
//
// Parameters:
//   - prompt: The user query or input text
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - The generated text response
//   - Any error that occurred during the process
func GenerateText(prompt string, options *GenerateTextOptions) (string, error) {
	return GetGeminiService().GenerateText(prompt, options)
}

// StreamText generates text using the Gemini API in streaming mode
//
// This function allows receiving the generated text in chunks as they are produced.
// It uses the singleton instance of GeminiService to handle the request.
//
// Parameters:
//   - prompt: The user query or input text
//   - callback: Function to call for each chunk of generated text
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - Any error that occurred during the process
func StreamText(prompt string, callback StreamTextCallback, options *GenerateTextOptions) error {
	return GetGeminiService().StreamText(prompt, callback, options)
}

// <PERSON><PERSON> conducts a conversation using the Gemini API
//
// This function allows for multi-turn conversations with message history.
// It uses the singleton instance of GeminiService to handle the request.
//
// Parameters:
//   - message: The current user message to send
//   - options: Configuration options including conversation history (can be nil for defaults)
//
// Returns:
//   - The generated response from the assistant
//   - Any error that occurred during the process
func Chat(message string, options *ChatOptions) (string, error) {
	return GetGeminiService().Chat(message, options)
}
