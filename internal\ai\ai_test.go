package ai

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewClient(t *testing.T) {
	// Test with default provider
	client := NewClient("openai-key", "deepseek-key", "xai-key", "grok-key", "gemini-key", "volcengine-key", "")
	assert.Equal(t, Provider("openai"), client.DefaultProvider)

	// Test with specified provider
	client = NewClient("openai-key", "deepseek-key", "xai-key", "grok-key", "gemini-key", "volcengine-key", "gemini")
	assert.Equal(t, Provider("gemini"), client.DefaultProvider)
}

func TestGenerateResponse_NoKey(t *testing.T) {
	// Test Gemini with no API key
	client := NewClient("openai-key", "deepseek-key", "xai-key", "grok-key", "", "volcengine-key", "gemini")
	response, err := client.GenerateResponse("Test prompt")
	assert.NoError(t, err)
	assert.Contains(t, response, "Google Gemini API key not configured")
}

// Note: We can't easily test the actual API call without mocking the external service
// This would require more complex test setup with HTTP mocking
