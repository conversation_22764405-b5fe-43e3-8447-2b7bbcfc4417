package auth

import (
	"testing"
	"time"

	"github.com/gin-ai-backend/internal/models"
	"github.com/stretchr/testify/assert"
)

func TestHashPasswordAndCheckPasswordHash(t *testing.T) {
	password := "securepassword"
	hash, err := HashPassword(password)
	assert.NoError(t, err)
	assert.NotEmpty(t, hash)
	isValid := CheckPasswordHash(password, hash)
	assert.True(t, isValid)
}

func TestGenerateAndValidateToken(t *testing.T) {
	user := models.User{
		Username: "testuser",
		Role:     "admin",
	}
	secret := "testsecret"

	token, err := GenerateToken(user, secret)
	assert.NoError(t, err)
	assert.NotEmpty(t, token)

	claims, err := ValidateToken(token, secret)
	assert.NoError(t, err)
	assert.Equal(t, user.Username, claims.Username)
	assert.Equal(t, user.Role, claims.Role)
	assert.WithinDuration(t, time.Now(), claims.RegisteredClaims.IssuedAt.Time, time.Second)
}
