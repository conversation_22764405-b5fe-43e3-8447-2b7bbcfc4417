package volcengine

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/joho/godotenv"
)

// VolcengineService is a client for the Volcengine AI service
// It handles authentication, configuration, and communication with the API
type VolcengineService struct {
	// APIKey is the authentication key for the Volcengine API
	APIKey string
	// APIURL is the endpoint URL for the Volcengine API
	APIURL string
	// ModelID is the identifier for the AI model to use
	ModelID string
}

// NewVolcengineService creates a new Volcengine service client
// It loads configuration from environment variables and sets default values if needed
func NewVolcengineService() *VolcengineService {
	// Try to load .env file
	_ = godotenv.Load()

	apiKey := os.Getenv("VOLCENGINE_API_KEY")
	apiURL := os.Getenv("VOLCENGINE_API_URL")
	modelID := os.Getenv("VOLCENGINE_MODEL_ID")

	// Set default values
	if apiURL == "" {
		apiURL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
	}
	if modelID == "" {
		modelID = "doubao-1.5-pro-32k-250115"
	}

	log.Println("Initializing VolcengineService...")
	service := &VolcengineService{
		APIKey:  apiKey,
		APIURL:  apiURL,
		ModelID: modelID,
	}

	if apiKey == "" {
		log.Println("Warning: VOLCENGINE_API_KEY not set in environment variables")
	} else {
		log.Println("VolcengineService initialized with API key")
	}

	log.Printf("Using Volcengine API URL: %s\n", apiURL)
	log.Printf("Using Volcengine model ID: %s\n", modelID)

	return service
}

// GenerateText generates text using the Volcengine API
//
// Parameters:
//   - systemPrompt: Instructions for the AI model about how to behave
//   - userPrompt: The actual user query or input text
//   - options: Configuration options for the generation (can be nil for defaults)
//
// Returns:
//   - The generated text response
//   - Any error that occurred during the process
func (s *VolcengineService) GenerateText(systemPrompt, userPrompt string, options *GenerateTextOptions) (string, error) {
	if options == nil {
		options = &GenerateTextOptions{}
	}

	// Set default values
	if options.Temperature == 0 {
		options.Temperature = 0.5
	}
	if options.MaxTokens == 0 {
		options.MaxTokens = 4096
	}
	if options.TopP == 0 {
		options.TopP = 0.7
	}
	if options.FrequencyPenalty == 0 {
		options.FrequencyPenalty = 0.3
	}
	if options.PresencePenalty == 0 {
		options.PresencePenalty = 0.1
	}
	if options.LogPrefix == "" {
		options.LogPrefix = "[Volcengine]"
	}

	if s.APIKey == "" {
		log.Printf("%s VOLCENGINE_API_KEY not set, cannot generate content\n", options.LogPrefix)
		return "", fmt.Errorf("VOLCENGINE_API_KEY not set")
	}

	log.Printf("%s Calling AI API...\n", options.LogPrefix)
	log.Printf("%s System prompt length: %d\n", options.LogPrefix, len(systemPrompt))
	log.Printf("%s User prompt length: %d\n", options.LogPrefix, len(userPrompt))

	// Build request data
	requestData := VolcengineRequestBody{
		Model: s.ModelID,
		Messages: []VolcengineMessage{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userPrompt},
		},
		MaxTokens:        options.MaxTokens,
		Temperature:      options.Temperature,
		TopP:             options.TopP,
		FrequencyPenalty: options.FrequencyPenalty,
		PresencePenalty:  options.PresencePenalty,
	}

	// Serialize request data
	requestJSON, err := json.Marshal(requestData)
	if err != nil {
		return "", fmt.Errorf("failed to serialize request data: %w", err)
	}

	// Set request headers
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + s.APIKey,
	}

	startTime := time.Now()
	log.Printf("%s Sending request to Volcengine AI...\n", options.LogPrefix)

	// Create HTTP request
	req, err := http.NewRequest("POST", s.APIURL, bytes.NewBuffer(requestJSON))
	if err != nil {
		return "", fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Add request headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// Send request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	elapsedTime := time.Since(startTime).Milliseconds()
	log.Printf("%s AI API response time: %dms, status code: %d\n", options.LogPrefix, elapsedTime, resp.StatusCode)

	// Check response status code
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API returned non-200 status code: %d", resp.StatusCode)
	}

	// Parse response
	var responseBody VolcengineResponseBody
	if err := json.NewDecoder(resp.Body).Decode(&responseBody); err != nil {
		return "", fmt.Errorf("failed to parse response: %w", err)
	}

	// Extract response content
	if len(responseBody.Choices) > 0 {
		content := responseBody.Choices[0].Message.Content
		log.Printf("%s Received AI response, length: %d\n", options.LogPrefix, len(content))
		return content, nil
	}

	log.Printf("%s Unexpected API response structure\n", options.LogPrefix)
	return "", fmt.Errorf("No content in API response")
}

// joinStrings is a helper function to join string slices with a separator
func joinStrings(strs []string, sep string) string {
	if len(strs) == 0 {
		return ""
	}

	result := strs[0]
	for i := 1; i < len(strs); i++ {
		result += sep + strs[i]
	}
	return result
}

// volcengineService is the singleton instance of VolcengineService
var volcengineService *VolcengineService

// GetVolcengineService returns the singleton instance of the Volcengine service
func GetVolcengineService() *VolcengineService {
	if volcengineService == nil {
		volcengineService = NewVolcengineService()
	}
	return volcengineService
}
