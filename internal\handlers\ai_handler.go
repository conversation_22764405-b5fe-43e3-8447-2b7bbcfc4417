package handlers

import (
	"net/http"

	"github.com/gin-ai-backend/internal/ai"
	"github.com/gin-ai-backend/internal/errors"
	"github.com/gin-ai-backend/internal/models"
	"github.com/gin-ai-backend/internal/validation"
	"github.com/gin-gonic/gin"
)

// AIHandler handles AI-related requests
type AIHandler struct {
	AIClient *ai.Client
}

// NewAIHandler creates a new AI handler
func NewAIHandler(openAIKey, deepSeekKey, xaiKey, grokKey, geminiKey, volcengineKey, defaultProvider string) *AIHandler {
	return &AIHandler{
		AIClient: ai.NewClient(openAIKey, deepSeekKey, xaiKey, grokKey, geminiKey, volcengineKey, defaultProvider),
	}
}

// GetAIClient returns the AI client
func (h *AIHandler) GetAIClient() *ai.Client {
	return h.AIClient
}

// GenerateResponse handles AI response generation
// @Summary Generate AI response
// @Description Generate a response from an AI model
// @Tags ai
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param provider query string false "AI provider (openai, deepseek, xai, gemini, volcengine)"
// @Param request body models.AIRequest true "AI Request"
// @Success 200 {object} models.AIResponse
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/ai/generate [post]
func (h *AIHandler) GenerateResponse(c *gin.Context) {
	var request models.AIRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		validation.HandleValidationErrors(c, err)
		return
	}

	// Validate provider if specified
	if request.Provider != "" {
		valid := false
		for _, p := range []string{"openai", "deepseek", "xai", "gemini", "volcengine"} {
			if request.Provider == p {
				valid = true
				break
			}
		}
		if !valid {
			errors.BadRequest(c, "Invalid AI provider. Must be one of: openai, deepseek, xai, gemini, volcengine", nil)
			return
		}
	}

	// Get user info from context (set by auth middleware)
	username, exists := c.Get("username")
	if !exists {
		errors.Unauthorized(c, "User not authenticated", nil)
		return
	}

	// Check if a specific provider was requested
	var provider ai.Provider
	providerStr := c.DefaultQuery("provider", "")
	if providerStr != "" {
		provider = ai.Provider(providerStr)
	}

	// Generate AI response with optional provider
	var response string
	var err error
	if providerStr != "" {
		response, err = h.AIClient.GenerateResponse(request.Prompt, provider)
	} else {
		response, err = h.AIClient.GenerateResponse(request.Prompt)
	}

	if err != nil {
		errors.InternalServerError(c, "Failed to generate AI response", err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user":     username,
		"prompt":   request.Prompt,
		"provider": providerStr,
		"response": response,
	})
}
