package models

import (
	"gorm.io/gorm"
)

// User represents a user in the system
type User struct {
	gorm.Model
	Username string `json:"username" gorm:"unique"`
	Email    string `json:"email" gorm:"unique"`
	Password string `json:"-"` // Password is not exposed in JSON responses
	Role     string `json:"role" gorm:"default:user"`
	APIToken string `json:"api_token,omitempty" gorm:"-"`
}

// UserLoginRequest represents the login request payload
type UserLoginRequest struct {
	Username string `json:"username" binding:"required" example:"johndoe"`
	Password string `json:"password" binding:"required" example:"Password123"`
}

// UserRegistrationRequest represents the registration request payload
type UserRegistrationRequest struct {
	Username string `json:"username" binding:"required,alphanum,min=3,max=30" example:"johndoe"`
	Email    string `json:"email" binding:"required,email" example:"<EMAIL>"`
	Password string `json:"password" binding:"required,min=8" example:"Password123"`
}

// AIRequest represents an AI request payload
type AIRequest struct {
	Prompt   string `json:"prompt" binding:"required,min=1,max=4000" example:"Tell me about artificial intelligence"`
	Provider string `json:"provider,omitempty" example:"openai"` // Optional provider (openai, deepseek, xai, gemini)
}

// AIResponse represents an AI response
type AIResponse struct {
	Provider string `json:"provider" example:"openai"`
	Response string `json:"response" example:"Artificial intelligence (AI) is intelligence demonstrated by machines..."`
}
