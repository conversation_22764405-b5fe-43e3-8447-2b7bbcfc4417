package pagination

import (
	"fmt"
	"math"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// PaginationQuery represents pagination query parameters
type PaginationQuery struct {
	Page     int `form:"page" binding:"min=1"`
	PageSize int `form:"page_size" binding:"min=1,max=100"`
}

// Paginate applies pagination to a GORM query
func Paginate(c *gin.Context, query *gorm.DB, result interface{}) (int64, error) {
	var pagination PaginationQuery
	if err := c.ShouldBindQuery(&pagination); err != nil {
		// Default values if not provided
		pagination.Page = 1
		pagination.PageSize = 10
	}

	// Count total items
	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count items: %w", err)
	}

	// Apply pagination
	offset := (pagination.Page - 1) * pagination.PageSize
	if err := query.Offset(offset).Limit(pagination.PageSize).Find(result).Error; err != nil {
		return 0, fmt.Errorf("failed to fetch page: %w", err)
	}

	return count, nil
}

// GetPaginationInfo returns pagination information
func GetPaginationInfo(c *gin.Context, count int64) map[string]interface{} {
	var pagination PaginationQuery
	if err := c.ShouldBindQuery(&pagination); err != nil {
		pagination.Page = 1
		pagination.PageSize = 10
	}

	totalPages := int(math.Ceil(float64(count) / float64(pagination.PageSize)))

	return map[string]interface{}{
		"current_page": pagination.Page,
		"page_size":    pagination.PageSize,
		"total_items":  count,
		"total_pages":  totalPages,
	}
}
