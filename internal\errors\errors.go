package errors

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ErrorResponse represents a standardized error response
type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// RespondWithError sends a standardized error response
func RespondWithError(c *gin.Context, status int, message string, err error) {
	details := ""
	if err != nil {
		details = err.Error()
	}
	c.JSO<PERSON>(status, ErrorResponse{
		Code:    status,
		Message: message,
		Details: details,
	})
}

// BadRequest sends a 400 Bad Request error
func BadRequest(c *gin.Context, message string, err error) {
	RespondWithError(c, http.StatusBadRequest, message, err)
}

// Unauthorized sends a 401 Unauthorized error
func Unauthorized(c *gin.Context, message string, err error) {
	if message == "" {
		message = "Unauthorized"
	}
	RespondWithError(c, http.StatusUnauthorized, message, err)
}

// Forbidden sends a 403 Forbidden error
func Forbidden(c *gin.Context, message string, err error) {
	if message == "" {
		message = "Forbidden"
	}
	RespondWithError(c, http.StatusForbidden, message, err)
}

// NotFound sends a 404 Not Found error
func NotFound(c *gin.Context, message string, err error) {
	if message == "" {
		message = "Resource not found"
	}
	RespondWithError(c, http.StatusNotFound, message, err)
}

// Conflict sends a 409 Conflict error
func Conflict(c *gin.Context, message string, err error) {
	RespondWithError(c, http.StatusConflict, message, err)
}

// InternalServerError sends a 500 Internal Server Error
func InternalServerError(c *gin.Context, message string, err error) {
	if message == "" {
		message = "Internal server error"
	}
	RespondWithError(c, http.StatusInternalServerError, message, err)
}

// TooManyRequests sends a 429 Too Many Requests error
func TooManyRequests(c *gin.Context, message string, err error) {
	if message == "" {
		message = "Too many requests"
	}
	RespondWithError(c, http.StatusTooManyRequests, message, err)
}
