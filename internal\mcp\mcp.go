package mcp

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/gin-ai-backend/internal/ai"
	"github.com/gin-ai-backend/internal/logging"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

// MCPServer represents the MCP server implementation
type MCPServer struct {
	server   *server.MCPServer
	aiClient *ai.Client
}

// NewMCPServer creates a new MCP server
func NewMCPServer(aiClient *ai.Client) *MCPServer {
	s := &MCPServer{
		aiClient: aiClient,
	}

	// Create MCP server
	mcpServer := server.NewMCPServer(
		"Gin AI Backend MCP Server",
		"1.0.0",
	)

	// Register tools
	s.registerTools(mcpServer)

	s.server = mcpServer
	return s
}

// registerTools registers all MCP tools
func (s *MCPServer) registerTools(mcpServer *server.MCPServer) {
	// Register generate text tool
	generateTextTool := mcp.NewTool("generate_text",
		mcp.WithDescription("Generate text using AI models"),
		mcp.WithString("prompt",
			mcp.Required(),
			mcp.Description("The prompt to generate text from"),
		),
		mcp.WithString("provider",
			mcp.Description("The AI provider to use (openai, deepseek, xai, grok, gemini, volcengine)"),
		),
	)

	mcpServer.AddTool(generateTextTool, s.handleGenerateText)

	// Register list providers tool
	listProvidersTool := mcp.NewTool("list_providers",
		mcp.WithDescription("List available AI providers"),
	)

	mcpServer.AddTool(listProvidersTool, s.handleListProviders)
}

// handleGenerateText handles the generate_text tool
func (s *MCPServer) handleGenerateText(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	prompt := request.Params.Arguments["prompt"].(string)

	var provider ai.Provider
	if providerStr, ok := request.Params.Arguments["provider"].(string); ok && providerStr != "" {
		provider = ai.Provider(strings.ToLower(providerStr))
	} else {
		provider = s.aiClient.DefaultProvider
	}

	logging.Info("MCP: Generating text", map[string]interface{}{
		"provider": provider,
		"prompt":   prompt[:min(len(prompt), 50)] + "...", // Log only the first 50 chars
	})

	response, err := s.aiClient.GenerateResponse(prompt, provider)
	if err != nil {
		return nil, fmt.Errorf("failed to generate text: %w", err)
	}

	// Use the provided helper function to create a text result
	return mcp.NewToolResultText(response), nil
}

// handleListProviders handles the list_providers tool
func (s *MCPServer) handleListProviders(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	providers := map[string]bool{
		"openai":     s.aiClient.OpenAIApiKey != "",
		"deepseek":   s.aiClient.DeepSeekApiKey != "",
		"xai":        s.aiClient.XAIApiKey != "",
		"grok":       s.aiClient.GrokApiKey != "",
		"gemini":     s.aiClient.GeminiApiKey != "",
		"volcengine": s.aiClient.VolcengineApiKey != "",
	}

	result := map[string]interface{}{
		"default":   string(s.aiClient.DefaultProvider),
		"available": providers,
	}

	// Convert result to JSON string
	jsonBytes, err := json.Marshal(result)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal result to JSON: %w", err)
	}

	// Use the provided helper function to create a text result with JSON content
	return mcp.NewToolResultText(string(jsonBytes)), nil
}

// Start starts the MCP server
func (s *MCPServer) Start() error {
	logging.Info("Starting MCP server", nil)

	// Check if we're running in a terminal
	if isTerminal() {
		return server.ServeStdio(s.server)
	}

	// Otherwise, serve on a TCP port
	port := os.Getenv("MCP_PORT")
	if port == "" {
		port = "8081"
	}

	logging.Info("MCP server listening on port "+port, nil)

	// Create an SSE server
	sseServer := server.NewSSEServer(s.server,
		server.WithSSEEndpoint("/"),
		server.WithMessageEndpoint("/"),
	)

	// Create HTTP server
	httpServer := &http.Server{
		Addr:    ":" + port,
		Handler: sseServer,
	}

	// Start the server
	return httpServer.ListenAndServe()
}

// isTerminal checks if the process is running in a terminal
func isTerminal() bool {
	// Simple check: if stdin is a terminal
	fileInfo, err := os.Stdin.Stat()
	if err != nil {
		log.Printf("Error checking stdin: %v", err)
		return false
	}

	// Check if stdin is a character device (terminal)
	return (fileInfo.Mode() & os.ModeCharDevice) != 0
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
